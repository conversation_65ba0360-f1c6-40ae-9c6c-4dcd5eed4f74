<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phonocardiograph - Heart Sound Recording & Analysis System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .clinical-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🫀 Phonocardiograph - Heart Sound Recording & Analysis System</h1>

        <div class="overview-section">
            <h2>Overview of Phonocardiography</h2>
            <p>The phonocardiograph is a sophisticated biomedical instrument designed to detect, amplify, record, and analyze heart sounds (phonocardiogram or PCG). This non-invasive diagnostic tool captures the acoustic vibrations produced by the heart during its cardiac cycle, including the characteristic S1 and S2 heart sounds, as well as murmurs, gallops, and other abnormal sounds. Modern phonocardiographs integrate advanced signal processing, digital filtering, and computer-aided analysis to provide detailed insights into cardiac function, valve performance, and structural abnormalities. The system serves as a valuable complement to electrocardiography and echocardiography in comprehensive cardiac assessment.</p>
        </div>

        <div class="section-title">🔧 Phonocardiograph Block Diagram & System Architecture</div>

        <div class="diagram-container">
            <svg width="100%" height="700" viewBox="0 0 1200 700">
                <!-- Title -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">PHONOCARDIOGRAPH SYSTEM BLOCK DIAGRAM</text>

                <!-- Input Stage - Microphone/Sensor -->
                <g>
                    <rect x="50" y="80" width="120" height="80" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="110" y="105" text-anchor="middle" font-size="12" font-weight="bold">ACOUSTIC SENSOR</text>
                    <text x="110" y="120" text-anchor="middle" font-size="10">• Piezoelectric microphone</text>
                    <text x="110" y="135" text-anchor="middle" font-size="10">• Contact transducer</text>
                    <text x="110" y="150" text-anchor="middle" font-size="10">• Frequency: 20-2000 Hz</text>

                    <!-- Heart illustration -->
                    <circle cx="110" cy="200" r="25" fill="#f44336" stroke="#c62828" stroke-width="2"/>
                    <text x="110" y="205" text-anchor="middle" font-size="10" fill="white">♥</text>
                    <text x="110" y="235" text-anchor="middle" font-size="10">Heart Sounds</text>
                </g>

                <!-- Pre-amplifier -->
                <g>
                    <rect x="220" y="80" width="120" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="280" y="105" text-anchor="middle" font-size="12" font-weight="bold">PRE-AMPLIFIER</text>
                    <text x="280" y="120" text-anchor="middle" font-size="10">• Low noise amplification</text>
                    <text x="280" y="135" text-anchor="middle" font-size="10">• Gain: 100-1000x</text>
                    <text x="280" y="150" text-anchor="middle" font-size="10">• High input impedance</text>
                </g>

                <!-- Filter Bank -->
                <g>
                    <rect x="390" y="80" width="120" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="450" y="105" text-anchor="middle" font-size="12" font-weight="bold">FILTER BANK</text>
                    <text x="450" y="120" text-anchor="middle" font-size="10">• High-pass: 20 Hz</text>
                    <text x="450" y="135" text-anchor="middle" font-size="10">• Low-pass: 2000 Hz</text>
                    <text x="450" y="150" text-anchor="middle" font-size="10">• Notch: 50/60 Hz</text>
                </g>

                <!-- Main Amplifier -->
                <g>
                    <rect x="560" y="80" width="120" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="620" y="105" text-anchor="middle" font-size="12" font-weight="bold">MAIN AMPLIFIER</text>
                    <text x="620" y="120" text-anchor="middle" font-size="10">• Variable gain control</text>
                    <text x="620" y="135" text-anchor="middle" font-size="10">• Automatic gain control</text>
                    <text x="620" y="150" text-anchor="middle" font-size="10">• Output: 0-5V</text>
                </g>

                <!-- ADC -->
                <g>
                    <rect x="730" y="80" width="120" height="80" fill="#bbdefb" stroke="#2196f3" stroke-width="3"/>
                    <text x="790" y="105" text-anchor="middle" font-size="12" font-weight="bold">ADC CONVERTER</text>
                    <text x="790" y="120" text-anchor="middle" font-size="10">• 16-bit resolution</text>
                    <text x="790" y="135" text-anchor="middle" font-size="10">• Sampling: 8-44 kHz</text>
                    <text x="790" y="150" text-anchor="middle" font-size="10">• Anti-aliasing filter</text>
                </g>

                <!-- Digital Processing -->
                <g>
                    <rect x="900" y="80" width="120" height="80" fill="#dcedc8" stroke="#689f38" stroke-width="3"/>
                    <text x="960" y="105" text-anchor="middle" font-size="12" font-weight="bold">DIGITAL PROCESSOR</text>
                    <text x="960" y="120" text-anchor="middle" font-size="10">• DSP algorithms</text>
                    <text x="960" y="135" text-anchor="middle" font-size="10">• Feature extraction</text>
                    <text x="960" y="150" text-anchor="middle" font-size="10">• Pattern recognition</text>
                </g>

                <!-- Signal flow arrows -->
                <path d="M 170 120 L 220 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 340 120 L 390 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 510 120 L 560 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 680 120 L 730 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 850 120 L 900 120" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                <!-- Output devices -->
                <g>
                    <text x="600" y="220" text-anchor="middle" font-size="16" font-weight="bold">OUTPUT & DISPLAY SYSTEMS</text>

                    <!-- Display -->
                    <rect x="200" y="250" width="150" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="275" y="275" text-anchor="middle" font-size="12" font-weight="bold">DISPLAY MONITOR</text>
                    <rect x="220" y="285" width="110" height="50" fill="#000" stroke="#333" stroke-width="1"/>
                    <path d="M 230 300 Q 250 290 270 300 Q 290 310 310 300 Q 330 290 350 300" stroke="#0f0" stroke-width="2" fill="none"/>
                    <text x="275" y="340" text-anchor="middle" font-size="10">Real-time waveform</text>

                    <!-- Recorder -->
                    <rect x="400" y="250" width="150" height="100" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="475" y="275" text-anchor="middle" font-size="12" font-weight="bold">CHART RECORDER</text>
                    <text x="475" y="295" text-anchor="middle" font-size="10">• Paper speed control</text>
                    <text x="475" y="310" text-anchor="middle" font-size="10">• Multi-channel recording</text>
                    <text x="475" y="325" text-anchor="middle" font-size="10">• Time/amplitude scales</text>
                    <text x="475" y="340" text-anchor="middle" font-size="10">Permanent record</text>

                    <!-- Computer interface -->
                    <rect x="600" y="250" width="150" height="100" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="675" y="275" text-anchor="middle" font-size="12" font-weight="bold">COMPUTER SYSTEM</text>
                    <text x="675" y="295" text-anchor="middle" font-size="10">• Data storage</text>
                    <text x="675" y="310" text-anchor="middle" font-size="10">• Analysis software</text>
                    <text x="675" y="325" text-anchor="middle" font-size="10">• Report generation</text>
                    <text x="675" y="340" text-anchor="middle" font-size="10">Digital analysis</text>

                    <!-- Audio output -->
                    <rect x="800" y="250" width="150" height="100" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="875" y="275" text-anchor="middle" font-size="12" font-weight="bold">AUDIO OUTPUT</text>
                    <circle cx="875" cy="305" r="15" fill="#333"/>
                    <text x="875" y="310" text-anchor="middle" font-size="10" fill="white">♪</text>
                    <text x="875" y="340" text-anchor="middle" font-size="10">Speaker/headphones</text>
                </g>

                <!-- Control panel -->
                <g>
                    <text x="200" y="420" font-size="16" font-weight="bold">CONTROL PANEL</text>
                    <rect x="50" y="440" width="300" height="120" fill="#f5f5f5" stroke="#666" stroke-width="2"/>

                    <!-- Controls -->
                    <circle cx="100" cy="480" r="15" fill="#ff9800"/>
                    <text x="100" y="485" text-anchor="middle" font-size="8">GAIN</text>
                    <text x="100" y="505" text-anchor="middle" font-size="9">Amplification</text>

                    <circle cx="150" cy="480" r="15" fill="#4caf50"/>
                    <text x="150" y="485" text-anchor="middle" font-size="8">FREQ</text>
                    <text x="150" y="505" text-anchor="middle" font-size="9">Filter select</text>

                    <circle cx="200" cy="480" r="15" fill="#2196f3"/>
                    <text x="200" y="485" text-anchor="middle" font-size="8">REC</text>
                    <text x="200" y="505" text-anchor="middle" font-size="9">Record</text>

                    <circle cx="250" cy="480" r="15" fill="#f44336"/>
                    <text x="250" y="485" text-anchor="middle" font-size="8">CAL</text>
                    <text x="250" y="505" text-anchor="middle" font-size="9">Calibration</text>

                    <circle cx="300" cy="480" r="15" fill="#9c27b0"/>
                    <text x="300" y="485" text-anchor="middle" font-size="8">MODE</text>
                    <text x="300" y="505" text-anchor="middle" font-size="9">Operation</text>

                    <!-- LED indicators -->
                    <text x="200" y="535" text-anchor="middle" font-size="12" font-weight="bold">STATUS INDICATORS</text>
                    <circle cx="150" cy="545" r="5" fill="#0f0"/>
                    <text x="170" y="550" font-size="10">Power ON</text>
                    <circle cx="220" cy="545" r="5" fill="#f00"/>
                    <text x="240" y="550" font-size="10">Recording</text>
                </g>

                <!-- Power supply -->
                <g>
                    <text x="600" y="420" font-size="16" font-weight="bold">POWER SUPPLY SYSTEM</text>
                    <rect x="450" y="440" width="300" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>

                    <text x="600" y="465" text-anchor="middle" font-size="12" font-weight="bold">REGULATED POWER SUPPLY</text>
                    <text x="600" y="485" text-anchor="middle" font-size="11">• Input: 110/220V AC, 50/60 Hz</text>
                    <text x="600" y="500" text-anchor="middle" font-size="11">• Output: ±15V, +5V DC</text>
                    <text x="600" y="515" text-anchor="middle" font-size="11">• Battery backup option</text>
                    <text x="600" y="530" text-anchor="middle" font-size="11">• Isolation transformer</text>
                    <text x="600" y="545" text-anchor="middle" font-size="11">• Low noise, high stability</text>
                </g>

                <!-- Calibration system -->
                <g>
                    <text x="950" y="420" font-size="16" font-weight="bold">CALIBRATION</text>
                    <rect x="850" y="440" width="200" height="120" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>

                    <text x="950" y="465" text-anchor="middle" font-size="12" font-weight="bold">CALIBRATION UNIT</text>
                    <text x="950" y="485" text-anchor="middle" font-size="10">• Internal tone generator</text>
                    <text x="950" y="500" text-anchor="middle" font-size="10">• Standard frequencies</text>
                    <text x="950" y="515" text-anchor="middle" font-size="10">• Amplitude reference</text>
                    <text x="950" y="530" text-anchor="middle" font-size="10">• System verification</text>
                    <text x="950" y="545" text-anchor="middle" font-size="10">• Auto-calibration</text>
                </g>

                <!-- Connection lines from processor to outputs -->
                <path d="M 960 160 L 960 200 L 275 200 L 275 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 960 200 L 475 200 L 475 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 960 200 L 675 200 L 675 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 960 200 L 875 200 L 875 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🎵 Heart Sound Components & Recording Setup</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>🔴 S1 Heart Sound (Lub)</h3>
                <p><strong>Origin:</strong> Mitral and tricuspid valve closure</p>
                <p><strong>Timing:</strong> Beginning of systole</p>
                <p><strong>Frequency:</strong> 20-200 Hz (low frequency)</p>
                <p><strong>Duration:</strong> 100-150 ms</p>
                <p><strong>Best Location:</strong> Apex of heart</p>
                <p><strong>Clinical Significance:</strong> Indicates ventricular contraction onset</p>
            </div>

            <div class="component-card">
                <h3>🔵 S2 Heart Sound (Dub)</h3>
                <p><strong>Origin:</strong> Aortic and pulmonary valve closure</p>
                <p><strong>Timing:</strong> End of systole</p>
                <p><strong>Frequency:</strong> 50-400 Hz (higher frequency)</p>
                <p><strong>Duration:</strong> 50-100 ms</p>
                <p><strong>Best Location:</strong> Base of heart</p>
                <p><strong>Clinical Significance:</strong> Indicates ventricular relaxation</p>
            </div>

            <div class="component-card">
                <h3>🟡 S3 Heart Sound (Gallop)</h3>
                <p><strong>Origin:</strong> Ventricular filling</p>
                <p><strong>Timing:</strong> Early diastole</p>
                <p><strong>Frequency:</strong> 20-50 Hz (very low)</p>
                <p><strong>Clinical Significance:</strong> May indicate heart failure</p>
                <p><strong>Normal in:</strong> Children, young adults, pregnancy</p>
                <p><strong>Pathological in:</strong> Adults >40 years</p>
            </div>

            <div class="component-card">
                <h3>🟠 S4 Heart Sound (Atrial Gallop)</h3>
                <p><strong>Origin:</strong> Atrial contraction against stiff ventricle</p>
                <p><strong>Timing:</strong> Late diastole (presystolic)</p>
                <p><strong>Frequency:</strong> 20-50 Hz (very low)</p>
                <p><strong>Clinical Significance:</strong> Indicates decreased ventricular compliance</p>
                <p><strong>Associated with:</strong> Hypertension, CAD, aortic stenosis</p>
            </div>

            <div class="component-card">
                <h3>🎵 Heart Murmurs</h3>
                <p><strong>Origin:</strong> Turbulent blood flow</p>
                <p><strong>Timing:</strong> Systolic or diastolic</p>
                <p><strong>Frequency:</strong> 100-1000 Hz (variable)</p>
                <p><strong>Grading:</strong> I/VI to VI/VI intensity</p>
                <p><strong>Types:</strong> Innocent vs pathological</p>
                <p><strong>Causes:</strong> Valve disease, septal defects, stenosis</p>
            </div>

            <div class="component-card">
                <h3>⚡ Recording Technique</h3>
                <p><strong>Patient Position:</strong> Supine, left lateral decubitus</p>
                <p><strong>Sensor Placement:</strong> Apex, base, tricuspid area</p>
                <p><strong>Contact Pressure:</strong> Light, consistent contact</p>
                <p><strong>Environment:</strong> Quiet room, minimal artifacts</p>
                <p><strong>Duration:</strong> 30-60 seconds per location</p>
                <p><strong>Breathing:</strong> Normal, held expiration for S2 splitting</p>
            </div>
        </div>

        <div class="section-title">🏥 Clinical Applications & Procedures</div>

        <div class="clinical-section">
            <h2>Phonocardiography in Clinical Practice</h2>
            <p>Phonocardiography serves as a valuable diagnostic tool in cardiology, providing objective documentation of heart sounds and murmurs. It complements physical examination by offering visual representation of acoustic events, enabling precise timing analysis, and facilitating comparison over time. The technique is particularly useful in pediatric cardiology, valve disease assessment, and monitoring treatment effectiveness.</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Clinical Application</th>
                    <th>Heart Sound Focus</th>
                    <th>Key Findings</th>
                    <th>Diagnostic Value</th>
                    <th>Limitations</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Valve Disease Assessment</strong></td>
                    <td>S1, S2, murmurs</td>
                    <td>Timing, intensity, frequency characteristics</td>
                    <td>Stenosis vs regurgitation differentiation</td>
                    <td>Cannot assess severity quantitatively</td>
                </tr>
                <tr>
                    <td><strong>Heart Failure Evaluation</strong></td>
                    <td>S3, S4 gallops</td>
                    <td>Presence of additional heart sounds</td>
                    <td>Early detection of ventricular dysfunction</td>
                    <td>Low sensitivity in mild cases</td>
                </tr>
                <tr>
                    <td><strong>Pediatric Screening</strong></td>
                    <td>Innocent murmurs</td>
                    <td>Benign vs pathological murmur characteristics</td>
                    <td>Reduces unnecessary referrals</td>
                    <td>Requires experienced interpretation</td>
                </tr>
                <tr>
                    <td><strong>Prosthetic Valve Monitoring</strong></td>
                    <td>Mechanical clicks</td>
                    <td>Normal vs abnormal prosthetic sounds</td>
                    <td>Early detection of valve dysfunction</td>
                    <td>Limited by prosthetic type variations</td>
                </tr>
                <tr>
                    <td><strong>Research Applications</strong></td>
                    <td>All components</td>
                    <td>Quantitative analysis of cardiac acoustics</td>
                    <td>Objective measurement for studies</td>
                    <td>Standardization challenges</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">⚙️ Technical Specifications & Maintenance</div>

        <div class="dual-column">
            <div class="clinical-section">
                <h3>System Specifications</h3>
                <ul>
                    <li><span class="highlight">Frequency Response:</span> 20 Hz - 2000 Hz (±3 dB)</li>
                    <li><span class="highlight">Sensitivity:</span> 0.1 Pa minimum detectable pressure</li>
                    <li><span class="highlight">Dynamic Range:</span> >80 dB</li>
                    <li><span class="highlight">Signal-to-Noise Ratio:</span> >60 dB</li>
                    <li><span class="highlight">Input Impedance:</span> >10 MΩ</li>
                    <li><span class="highlight">Sampling Rate:</span> 8-44 kHz (adjustable)</li>
                    <li><span class="highlight">Resolution:</span> 16-bit ADC minimum</li>
                    <li><span class="highlight">Power Consumption:</span> <50W</li>
                </ul>
            </div>

            <div class="clinical-section">
                <h3>Calibration & Maintenance</h3>
                <ul>
                    <li><span class="highlight">Daily:</span> System power-up check, LED indicators</li>
                    <li><span class="highlight">Weekly:</span> Sensor cleaning, cable inspection</li>
                    <li><span class="highlight">Monthly:</span> Calibration verification with test signals</li>
                    <li><span class="highlight">Quarterly:</span> Full system calibration</li>
                    <li><span class="highlight">Annually:</span> Professional service and validation</li>
                    <li><span class="highlight">Sensor Care:</span> Gentle cleaning, avoid moisture</li>
                    <li><span class="highlight">Storage:</span> Dry environment, temperature controlled</li>
                    <li><span class="highlight">Documentation:</span> Maintenance log required</li>
                </ul>
            </div>
        </div>

        <div class="section-title">🔧 Troubleshooting & Safety</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>⚠️ Common Issues</h3>
                <p><strong>No Signal:</strong> Check sensor contact, cable connections</p>
                <p><strong>Excessive Noise:</strong> Verify grounding, filter settings</p>
                <p><strong>Distorted Signal:</strong> Adjust gain, check for overload</p>
                <p><strong>Intermittent Recording:</strong> Inspect cables for damage</p>
                <p><strong>Calibration Drift:</strong> Perform system recalibration</p>
                <p><strong>Poor Audio Quality:</strong> Clean sensor, check amplifier</p>
            </div>

            <div class="component-card">
                <h3>🛡️ Safety Considerations</h3>
                <p><strong>Electrical Safety:</strong> Isolated patient circuits</p>
                <p><strong>Infection Control:</strong> Disposable sensor covers</p>
                <p><strong>Patient Comfort:</strong> Gentle sensor pressure</p>
                <p><strong>EMI Protection:</strong> Shielded cables and circuits</p>
                <p><strong>Grounding:</strong> Proper earth connection</p>
                <p><strong>Emergency Procedures:</strong> Quick disconnect capability</p>
            </div>

            <div class="component-card">
                <h3>📊 Quality Assurance</h3>
                <p><strong>Signal Quality:</strong> Monitor SNR continuously</p>
                <p><strong>Artifact Detection:</strong> Automated algorithms</p>
                <p><strong>Baseline Stability:</strong> DC offset monitoring</p>
                <p><strong>Frequency Response:</strong> Regular verification</p>
                <p><strong>Documentation:</strong> All recordings timestamped</p>
                <p><strong>Backup Systems:</strong> Redundant data storage</p>
            </div>
        </div>

    </div>
</body>
</html>
