<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 2: Distributed Databases & Blockchain Technology</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Question 2: Distributed Databases & Their Relationship to Blockchain Technology</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What is a Distributed Database?</h2>

            <div class="concept-box">
                <h3>Definition and Core Concepts</h3>
                <p>A <span class="highlight">distributed database</span> is a collection of multiple, logically interrelated databases distributed over a computer network. Unlike traditional centralized databases where all data resides on a single server, distributed databases store data across multiple physical locations while appearing as a single logical database to users.</p>

                <p><strong>Key Characteristics:</strong></p>
                <ul>
                    <li><strong>Physical Distribution:</strong> Data is stored across multiple sites/nodes</li>
                    <li><strong>Logical Unity:</strong> Appears as a single database to applications</li>
                    <li><strong>Network Connectivity:</strong> Sites connected via communication networks</li>
                    <li><strong>Local Autonomy:</strong> Each site can operate independently</li>
                    <li><strong>Transparency:</strong> Users don't need to know data location</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="600" viewBox="0 0 1000 600">
                    <!-- Title -->
                    <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold">DISTRIBUTED DATABASE ARCHITECTURE</text>

                    <!-- Centralized vs Distributed Comparison -->
                    <text x="200" y="70" text-anchor="middle" font-size="16" font-weight="bold">CENTRALIZED DATABASE</text>
                    <text x="800" y="70" text-anchor="middle" font-size="16" font-weight="bold">DISTRIBUTED DATABASE</text>

                    <!-- Centralized Database -->
                    <g>
                        <!-- Central Server -->
                        <rect x="150" y="100" width="100" height="80" fill="#FFE0B2" stroke="#FF9800" stroke-width="3"/>
                        <text x="200" y="130" text-anchor="middle" font-size="12" font-weight="bold">DATABASE</text>
                        <text x="200" y="145" text-anchor="middle" font-size="12" font-weight="bold">SERVER</text>
                        <text x="200" y="160" text-anchor="middle" font-size="10">All Data</text>

                        <!-- Clients -->
                        <circle cx="100" cy="220" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="100" y="225" text-anchor="middle" font-size="10">Client 1</text>

                        <circle cx="200" cy="220" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="200" y="225" text-anchor="middle" font-size="10">Client 2</text>

                        <circle cx="300" cy="220" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="300" y="225" text-anchor="middle" font-size="10">Client 3</text>

                        <!-- Connections -->
                        <line x1="120" y1="210" x2="180" y2="180" stroke="#2196F3" stroke-width="2"/>
                        <line x1="200" y1="200" x2="200" y2="180" stroke="#2196F3" stroke-width="2"/>
                        <line x1="280" y1="210" x2="220" y2="180" stroke="#2196F3" stroke-width="2"/>

                        <!-- Single Point of Failure -->
                        <text x="200" y="270" text-anchor="middle" font-size="12" fill="#F44336" font-weight="bold">Single Point of Failure</text>
                    </g>

                    <!-- Distributed Database -->
                    <g>
                        <!-- Multiple Database Nodes -->
                        <rect x="700" y="100" width="80" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="740" y="125" text-anchor="middle" font-size="10" font-weight="bold">DB Node 1</text>
                        <text x="740" y="140" text-anchor="middle" font-size="9">Data A, B</text>

                        <rect x="820" y="100" width="80" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="860" y="125" text-anchor="middle" font-size="10" font-weight="bold">DB Node 2</text>
                        <text x="860" y="140" text-anchor="middle" font-size="9">Data C, D</text>

                        <rect x="700" y="180" width="80" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="740" y="205" text-anchor="middle" font-size="10" font-weight="bold">DB Node 3</text>
                        <text x="740" y="220" text-anchor="middle" font-size="9">Data E, F</text>

                        <rect x="820" y="180" width="80" height="60" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="860" y="205" text-anchor="middle" font-size="10" font-weight="bold">DB Node 4</text>
                        <text x="860" y="220" text-anchor="middle" font-size="9">Replica A, B</text>

                        <!-- Network connections between nodes -->
                        <line x1="780" y1="130" x2="820" y2="130" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="740" y1="160" x2="740" y2="180" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="860" y1="160" x2="860" y2="180" stroke="#4CAF50" stroke-width="2"/>
                        <line x1="780" y1="210" x2="820" y2="210" stroke="#4CAF50" stroke-width="2"/>

                        <!-- Clients -->
                        <circle cx="650" cy="280" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="650" y="285" text-anchor="middle" font-size="10">Client A</text>

                        <circle cx="750" cy="280" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="750" y="285" text-anchor="middle" font-size="10">Client B</text>

                        <circle cx="850" cy="280" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="850" y="285" text-anchor="middle" font-size="10">Client C</text>

                        <circle cx="950" cy="280" r="20" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="950" y="285" text-anchor="middle" font-size="10">Client D</text>

                        <!-- Client connections to nearest nodes -->
                        <line x1="670" y1="270" x2="720" y2="240" stroke="#2196F3" stroke-width="2"/>
                        <line x1="750" y1="260" x2="750" y2="240" stroke="#2196F3" stroke-width="2"/>
                        <line x1="850" y1="260" x2="850" y2="240" stroke="#2196F3" stroke-width="2"/>
                        <line x1="930" y1="270" x2="880" y2="240" stroke="#2196F3" stroke-width="2"/>

                        <!-- Fault Tolerance -->
                        <text x="800" y="320" text-anchor="middle" font-size="12" fill="#4CAF50" font-weight="bold">Fault Tolerant</text>
                    </g>

                    <!-- Distributed Database Components -->
                    <text x="500" y="380" text-anchor="middle" font-size="16" font-weight="bold">DISTRIBUTED DATABASE COMPONENTS</text>

                    <!-- DDBMS -->
                    <rect x="100" y="400" width="150" height="80" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                    <text x="175" y="425" text-anchor="middle" font-size="12" font-weight="bold">DDBMS</text>
                    <text x="175" y="440" text-anchor="middle" font-size="10">Distributed Database</text>
                    <text x="175" y="455" text-anchor="middle" font-size="10">Management System</text>
                    <text x="175" y="470" text-anchor="middle" font-size="10">• Query Processing</text>

                    <!-- Transaction Manager -->
                    <rect x="300" y="400" width="150" height="80" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                    <text x="375" y="425" text-anchor="middle" font-size="12" font-weight="bold">TRANSACTION</text>
                    <text x="375" y="440" text-anchor="middle" font-size="12" font-weight="bold">MANAGER</text>
                    <text x="375" y="455" text-anchor="middle" font-size="10">• ACID Properties</text>
                    <text x="375" y="470" text-anchor="middle" font-size="10">• Concurrency Control</text>

                    <!-- Data Dictionary -->
                    <rect x="500" y="400" width="150" height="80" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                    <text x="575" y="425" text-anchor="middle" font-size="12" font-weight="bold">DATA</text>
                    <text x="575" y="440" text-anchor="middle" font-size="12" font-weight="bold">DICTIONARY</text>
                    <text x="575" y="455" text-anchor="middle" font-size="10">• Metadata</text>
                    <text x="575" y="470" text-anchor="middle" font-size="10">• Schema Information</text>

                    <!-- Communication Manager -->
                    <rect x="700" y="400" width="150" height="80" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
                    <text x="775" y="425" text-anchor="middle" font-size="12" font-weight="bold">COMMUNICATION</text>
                    <text x="775" y="440" text-anchor="middle" font-size="12" font-weight="bold">MANAGER</text>
                    <text x="775" y="455" text-anchor="middle" font-size="10">• Network Protocols</text>
                    <text x="775" y="470" text-anchor="middle" font-size="10">• Message Passing</text>

                    <!-- CAP Theorem -->
                    <text x="500" y="530" text-anchor="middle" font-size="16" font-weight="bold">CAP THEOREM</text>

                    <circle cx="350" cy="570" r="30" fill="#FFE0B2" stroke="#FF9800" stroke-width="2"/>
                    <text x="350" y="575" text-anchor="middle" font-size="12" font-weight="bold">C</text>
                    <text x="350" y="600" text-anchor="middle" font-size="10">Consistency</text>

                    <circle cx="500" cy="570" r="30" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                    <text x="500" y="575" text-anchor="middle" font-size="12" font-weight="bold">A</text>
                    <text x="500" y="600" text-anchor="middle" font-size="10">Availability</text>

                    <circle cx="650" cy="570" r="30" fill="#FFCDD2" stroke="#F44336" stroke-width="2"/>
                    <text x="650" y="575" text-anchor="middle" font-size="12" font-weight="bold">P</text>
                    <text x="650" y="600" text-anchor="middle" font-size="10">Partition Tolerance</text>

                    <text x="500" y="620" text-anchor="middle" font-size="12" font-style="italic">Pick Any Two</text>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🔗</span>How Distributed Databases Relate to Blockchain</h2>

            <div class="concept-box">
                <h3>Blockchain as a Special Type of Distributed Database</h3>
                <p>Blockchain can be viewed as a specialized form of distributed database with unique characteristics that distinguish it from traditional distributed databases.</p>

                <p><strong>Key Similarities:</strong></p>
                <ul>
                    <li><strong>Distributed Storage:</strong> Data replicated across multiple nodes</li>
                    <li><strong>Network Communication:</strong> Nodes communicate over networks</li>
                    <li><strong>Consensus Requirements:</strong> Nodes must agree on data state</li>
                    <li><strong>Fault Tolerance:</strong> System continues despite node failures</li>
                </ul>

                <p><strong>Key Differences:</strong></p>
                <ul>
                    <li><strong>Immutability:</strong> Blockchain data cannot be modified once written</li>
                    <li><strong>Decentralization:</strong> No central authority or administrator</li>
                    <li><strong>Transparency:</strong> All participants can verify entire history</li>
                    <li><strong>Cryptographic Security:</strong> Heavy use of cryptographic hashing</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Advantages of Distributed Databases</h2>

            <div class="advantages-box">
                <h3>Benefits and Strengths</h3>
                <ul>
                    <li><strong>Reliability & Availability:</strong> No single point of failure</li>
                    <li><strong>Scalability:</strong> Can handle increased load by adding nodes</li>
                    <li><strong>Performance:</strong> Data locality reduces access time</li>
                    <li><strong>Geographic Distribution:</strong> Data closer to users worldwide</li>
                    <li><strong>Cost-Effectiveness:</strong> Use commodity hardware</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Disadvantages and Challenges</h2>

            <div class="disadvantages-box">
                <h3>Limitations and Complexities</h3>
                <ul>
                    <li><strong>Complexity:</strong> Much more complex to design and maintain</li>
                    <li><strong>Security Challenges:</strong> Multiple access points increase risks</li>
                    <li><strong>Consistency Issues:</strong> CAP theorem limitations</li>
                    <li><strong>Network Dependency:</strong> Performance depends on network</li>
                    <li><strong>Higher Costs:</strong> Initial setup and maintenance</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Real-World Applications</h2>

            <div class="example-box">
                <h3>Blockchain Examples</h3>
                <ul>
                    <li><strong>Bitcoin:</strong> Distributed ledger for cryptocurrency</li>
                    <li><strong>Ethereum:</strong> Platform for smart contracts and DApps</li>
                    <li><strong>Hyperledger:</strong> Enterprise blockchain solutions</li>
                    <li><strong>IPFS:</strong> Distributed file storage system</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Traditional Examples</h3>
                <ul>
                    <li><strong>Social Media:</strong> Facebook, Twitter global data distribution</li>
                    <li><strong>E-commerce:</strong> Amazon, eBay product catalogs</li>
                    <li><strong>Banking:</strong> Financial transaction processing</li>
                    <li><strong>Content Delivery:</strong> Netflix, YouTube streaming</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>