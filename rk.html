<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automation Testing Framework</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .framework-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .types-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .implementation-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .step {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .benefits-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .flow-arrow {
            stroke: #e74c3c;
            stroke-width: 3;
            fill: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Automation Testing Framework</h1>
        
        <div class="overview-section">
            <h2>Framework Overview</h2>
            <p>An automation testing framework is a structured approach to creating and implementing automated test scripts. It provides guidelines, coding standards, test data management, object repository, and other supporting modules that enable efficient test automation. The framework acts as a foundation that supports automated testing by providing reusable components, standardized practices, and systematic organization of test assets.</p>
        </div>

        <div class="section-title">🏗️ Complete Automation Framework Architecture</div>
        
        <div class="framework-diagram">
            <svg width="100%" height="800" viewBox="0 0 1400 800">
                <!-- Test Management Layer -->
                <g>
                    <rect x="50" y="50" width="1300" height="80" fill="#2c3e50" stroke="#1a252f" stroke-width="2" rx="5"/>
                    <text x="700" y="85" text-anchor="middle" font-size="18" font-weight="bold" fill="white">TEST MANAGEMENT LAYER</text>
                    <text x="700" y="105" text-anchor="middle" font-size="12" fill="white">Test Planning • Test Strategy • Requirements • Test Cases • Defect Management</text>
                </g>

                <!-- Test Automation Framework Core -->
                <g>
                    <rect x="100" y="170" width="1200" height="60" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="5"/>
                    <text x="700" y="195" text-anchor="middle" font-size="16" font-weight="bold" fill="white">AUTOMATION FRAMEWORK CORE</text>
                    <text x="700" y="215" text-anchor="middle" font-size="11" fill="white">Framework Design Patterns • Configuration Management • Logging & Reporting</text>
                </g>

                <!-- Test Script Layer -->
                <g>
                    <rect x="50" y="270" width="400" height="120" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                    <text x="250" y="300" text-anchor="middle" font-size="14" font-weight="bold" fill="white">TEST SCRIPT LAYER</text>
                    
                    <!-- Test Scripts -->
                    <rect x="70" y="320" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="120" y="338" text-anchor="middle" font-size="9" fill="white">Functional Tests</text>
                    
                    <rect x="180" y="320" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="230" y="338" text-anchor="middle" font-size="9" fill="white">Regression Tests</text>
                    
                    <rect x="290" y="320" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="340" y="338" text-anchor="middle" font-size="9" fill="white">API Tests</text>
                    
                    <rect x="70" y="355" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="120" y="373" text-anchor="middle" font-size="9" fill="white">UI Tests</text>
                    
                    <rect x="180" y="355" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="230" y="373" text-anchor="middle" font-size="9" fill="white">Database Tests</text>
                    
                    <rect x="290" y="355" width="100" height="30" fill="#2980b9" stroke="#1f4e79" stroke-width="1" rx="3"/>
                    <text x="340" y="373" text-anchor="middle" font-size="9" fill="white">Performance Tests</text>
                </g>

                <!-- Test Data Layer -->
                <g>
                    <rect x="500" y="270" width="400" height="120" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="5"/>
                    <text x="700" y="300" text-anchor="middle" font-size="14" font-weight="bold" fill="white">TEST DATA LAYER</text>
                    
                    <!-- Data Sources -->
                    <rect x="520" y="320" width="80" height="30" fill="#d35400" stroke="#a04000" stroke-width="1" rx="3"/>
                    <text x="560" y="330" text-anchor="middle" font-size="8" fill="white">Excel Files</text>
                    <text x="560" y="342" text-anchor="middle" font-size="8" fill="white">.xlsx/.csv</text>
                    
                    <rect x="610" y="320" width="80" height="30" fill="#d35400" stroke="#a04000" stroke-width="1" rx="3"/>
                    <text x="650" y="330" text-anchor="middle" font-size="8" fill="white">JSON/XML</text>
                    <text x="650" y="342" text-anchor="middle" font-size="8" fill="white">Config Files</text>
                    
                    <rect x="700" y="320" width="80" height="30" fill="#d35400" stroke="#a04000" stroke-width="1" rx="3"/>
                    <text x="740" y="330" text-anchor="middle" font-size="8" fill="white">Database</text>
                    <text x="740" y="342" text-anchor="middle" font-size="8" fill="white">SQL/NoSQL</text>
                    
                    <rect x="790" y="320" width="80" height="30" fill="#d35400" stroke="#a04000" stroke-width="1" rx="3"/>
                    <text x="830" y="330" text-anchor="middle" font-size="8" fill="white">Properties</text>
                    <text x="830" y="342" text-anchor="middle" font-size="8" fill="white">Files</text>
                    
                    <!-- Data Management -->
                    <rect x="580" y="355" width="120" height="30" fill="#e67e22" stroke="#d35400" stroke-width="1" rx="3"/>
                    <text x="640" y="370" text-anchor="middle" font-size="9" fill="white">Data Provider Classes</text>
                    <text x="640" y="382" text-anchor="middle" font-size="9" fill="white">Data Utilities</text>
                </g>

                <!-- Object Repository -->
                <g>
                    <rect x="950" y="270" width="400" height="120" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                    <text x="1150" y="300" text-anchor="middle" font-size="14" font-weight="bold" fill="white">OBJECT REPOSITORY</text>
                    
                    <!-- Page Objects -->
                    <rect x="970" y="320" width="90" height="30" fill="#8e44ad" stroke="#7d3c98" stroke-width="1" rx="3"/>
                    <text x="1015" y="330" text-anchor="middle" font-size="8" fill="white">Page Objects</text>
                    <text x="1015" y="342" text-anchor="middle" font-size="8" fill="white">POM Pattern</text>
                    
                    <rect x="1070" y="320" width="90" height="30" fill="#8e44ad" stroke="#7d3c98" stroke-width="1" rx="3"/>
                    <text x="1115" y="330" text-anchor="middle" font-size="8" fill="white">Locators</text>
                    <text x="1115" y="342" text-anchor="middle" font-size="8" fill="white">Repository</text>
                    
                    <rect x="1170" y="320" width="90" height="30" fill="#8e44ad" stroke="#7d3c98" stroke-width="1" rx="3"/>
                    <text x="1215" y="330" text-anchor="middle" font-size="8" fill="white">Element</text>
                    <text x="1215" y="342" text-anchor="middle" font-size="8" fill="white">Factory</text>
                    
                    <rect x="1270" y="320" width="90" height="30" fill="#8e44ad" stroke="#7d3c98" stroke-width="1" rx="3"/>
                    <text x="1315" y="330" text-anchor="middle" font-size="8" fill="white">Web Elements</text>
                    <text x="1315" y="342" text-anchor="middle" font-size="8" fill="white">Library</text>
                    
                    <!-- Object Management -->
                    <rect x="1070" y="355" width="180" height="30" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="3"/>
                    <text x="1160" y="370" text-anchor="middle" font-size="9" fill="white">Object Management Utilities</text>
                    <text x="1160" y="382" text-anchor="middle" font-size="9" fill="white">Dynamic Object Creation</text>
                </g>

                <!-- Utility Layer -->
                <g>
                    <rect x="50" y="430" width="1300" height="100" fill="#27ae60" stroke="#1e8449" stroke-width="2" rx="5"/>
                    <text x="700" y="460" text-anchor="middle" font-size="16" font-weight="bold" fill="white">UTILITY & SUPPORT LAYER</text>
                    
                    <!-- Utilities -->
                    <rect x="80" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="140" y="495" text-anchor="middle" font-size="9" fill="white">Browser Utilities</text>
                    <text x="140" y="507" text-anchor="middle" font-size="9" fill="white">Driver Management</text>
                    
                    <rect x="220" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="280" y="495" text-anchor="middle" font-size="9" fill="white">Wait Utilities</text>
                    <text x="280" y="507" text-anchor="middle" font-size="9" fill="white">Explicit/Implicit</text>
                    
                    <rect x="360" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="420" y="495" text-anchor="middle" font-size="9" fill="white">Screenshot Utils</text>
                    <text x="420" y="507" text-anchor="middle" font-size="9" fill="white">Evidence Capture</text>
                    
                    <rect x="500" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="560" y="495" text-anchor="middle" font-size="9" fill="white">File Utilities</text>
                    <text x="560" y="507" text-anchor="middle" font-size="9" fill="white">I/O Operations</text>
                    
                    <rect x="640" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="700" y="495" text-anchor="middle" font-size="9" fill="white">Database Utils</text>
                    <text x="700" y="507" text-anchor="middle" font-size="9" fill="white">Connection Mgmt</text>
                    
                    <rect x="780" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="840" y="495" text-anchor="middle" font-size="9" fill="white">API Utilities</text>
                    <text x="840" y="507" text-anchor="middle" font-size="9" fill="white">REST/SOAP</text>
                    
                    <rect x="920" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="980" y="495" text-anchor="middle" font-size="9" fill="white">Email Utilities</text>
                    <text x="980" y="507" text-anchor="middle" font-size="9" fill="white">Mail Validation</text>
                    
                    <rect x="1060" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="1120" y="495" text-anchor="middle" font-size="9" fill="white">Log Utilities</text>
                    <text x="1120" y="507" text-anchor="middle" font-size="9" fill="white">Log4j/Logback</text>
                    
                    <rect x="1200" y="480" width="120" height="30" fill="#1e8449" stroke="#145a32" stroke-width="1" rx="3"/>
                    <text x="1260" y="495" text-anchor="middle" font-size="9" fill="white">Config Manager</text>
                    <text x="1260" y="507" text-anchor="middle" font-size="9" fill="white">Environment Setup</text>
                