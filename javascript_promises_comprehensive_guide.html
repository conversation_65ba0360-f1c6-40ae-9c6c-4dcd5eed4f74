<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Promises - Comprehensive Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .promise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .promise-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .promise-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .promise-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .basics-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .methods-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        .advanced-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border-left: 4px solid #4299e1;
        }
        .code-block .keyword {
            color: #f56565;
        }
        .code-block .string {
            color: #68d391;
        }
        .code-block .comment {
            color: #a0aec0;
            font-style: italic;
        }
        .code-block .function {
            color: #fbb6ce;
        }
        .interactive-demo {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .demo-button:hover {
            background: #0056b3;
        }
        .demo-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            min-height: 50px;
            font-family: monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .state-indicator {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .pending { background: #ffc107; color: #212529; }
        .fulfilled { background: #28a745; color: white; }
        .rejected { background: #dc3545; color: white; }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JavaScript Promises - Comprehensive Guide</h1>

        <div class="overview-section">
            <h2>Mastering Asynchronous JavaScript with Promises</h2>
            <p>JavaScript Promises represent a revolutionary approach to handling asynchronous operations, providing a clean and powerful alternative to callback-based programming. Promises offer a structured way to manage asynchronous code execution, error handling, and complex operation chaining. This comprehensive guide explores Promise fundamentals, advanced patterns, modern async/await syntax, and real-world applications that demonstrate how Promises have transformed JavaScript development by eliminating callback hell and enabling more readable, maintainable asynchronous code.</p>
        </div>

        <div class="section-title">🔄 Promise States and Lifecycle</div>

        <div class="diagram-container">
            <svg width="100%" height="600" viewBox="0 0 1200 600">
                <!-- Promise Lifecycle Diagram -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">JAVASCRIPT PROMISE LIFECYCLE</text>

                <!-- Pending State -->
                <g>
                    <circle cx="200" cy="150" r="60" fill="#ffc107" stroke="#f39c12" stroke-width="4"/>
                    <text x="200" y="145" text-anchor="middle" font-size="16" font-weight="bold">PENDING</text>
                    <text x="200" y="165" text-anchor="middle" font-size="12">Initial State</text>

                    <rect x="120" y="220" width="160" height="80" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
                    <text x="200" y="240" text-anchor="middle" font-size="12" font-weight="bold">Characteristics:</text>
                    <text x="200" y="255" text-anchor="middle" font-size="11">• Operation in progress</text>
                    <text x="200" y="270" text-anchor="middle" font-size="11">• Neither fulfilled nor rejected</text>
                    <text x="200" y="285" text-anchor="middle" font-size="11">• Can transition to either state</text>
                </g>

                <!-- Fulfilled State -->
                <g>
                    <circle cx="500" cy="150" r="60" fill="#28a745" stroke="#20c997" stroke-width="4"/>
                    <text x="500" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="white">FULFILLED</text>
                    <text x="500" y="165" text-anchor="middle" font-size="12" fill="white">Resolved</text>

                    <rect x="420" y="220" width="160" height="80" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="10"/>
                    <text x="500" y="240" text-anchor="middle" font-size="12" font-weight="bold">Characteristics:</text>
                    <text x="500" y="255" text-anchor="middle" font-size="11">• Operation completed successfully</text>
                    <text x="500" y="270" text-anchor="middle" font-size="11">• Has a resolved value</text>
                    <text x="500" y="285" text-anchor="middle" font-size="11">• Immutable state</text>
                </g>

                <!-- Rejected State -->
                <g>
                    <circle cx="800" cy="150" r="60" fill="#dc3545" stroke="#c82333" stroke-width="4"/>
                    <text x="800" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="white">REJECTED</text>
                    <text x="800" y="165" text-anchor="middle" font-size="12" fill="white">Error</text>

                    <rect x="720" y="220" width="160" height="80" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="10"/>
                    <text x="800" y="240" text-anchor="middle" font-size="12" font-weight="bold">Characteristics:</text>
                    <text x="800" y="255" text-anchor="middle" font-size="11">• Operation failed</text>
                    <text x="800" y="270" text-anchor="middle" font-size="11">• Has a rejection reason</text>
                    <text x="800" y="285" text-anchor="middle" font-size="11">• Immutable state</text>
                </g>

                <!-- Transitions -->
                <path d="M 260 130 L 440 130" stroke="#28a745" stroke-width="4" fill="none" marker-end="url(#greenArrow)"/>
                <text x="350" y="120" text-anchor="middle" font-size="12" fill="#28a745" font-weight="bold">resolve(value)</text>

                <path d="M 260 170 L 740 170" stroke="#dc3545" stroke-width="4" fill="none" marker-end="url(#redArrow)"/>
                <text x="500" y="190" text-anchor="middle" font-size="12" fill="#dc3545" font-weight="bold">reject(reason)</text>

                <!-- Promise Creation -->
                <g>
                    <text x="600" y="380" text-anchor="middle" font-size="18" font-weight="bold">PROMISE CREATION PATTERNS</text>

                    <!-- Constructor Pattern -->
                    <rect x="100" y="400" width="300" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
                    <text x="250" y="425" text-anchor="middle" font-size="14" font-weight="bold">Constructor Pattern</text>
                    <text x="120" y="445" font-size="11" font-family="monospace">new Promise((resolve, reject) => {</text>
                    <text x="130" y="460" font-size="11" font-family="monospace">// Async operation</text>
                    <text x="130" y="475" font-size="11" font-family="monospace">if (success) resolve(value);</text>
                    <text x="130" y="490" font-size="11" font-family="monospace">else reject(error);</text>
                    <text x="120" y="505" font-size="11" font-family="monospace">});</text>

                    <!-- Static Methods -->
                    <rect x="450" y="400" width="300" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="10"/>
                    <text x="600" y="425" text-anchor="middle" font-size="14" font-weight="bold">Static Methods</text>
                    <text x="470" y="445" font-size="11" font-family="monospace">Promise.resolve(value)</text>
                    <text x="470" y="460" font-size="11" font-family="monospace">Promise.reject(reason)</text>
                    <text x="470" y="475" font-size="11" font-family="monospace">Promise.all([promises])</text>
                    <text x="470" y="490" font-size="11" font-family="monospace">Promise.race([promises])</text>
                    <text x="470" y="505" font-size="11" font-family="monospace">Promise.allSettled([promises])</text>

                    <!-- Async/Await -->
                    <rect x="800" y="400" width="300" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="950" y="425" text-anchor="middle" font-size="14" font-weight="bold">Async/Await Syntax</text>
                    <text x="820" y="445" font-size="11" font-family="monospace">async function example() {</text>
                    <text x="830" y="460" font-size="11" font-family="monospace">try {</text>
                    <text x="840" y="475" font-size="11" font-family="monospace">const result = await promise;</text>
                    <text x="830" y="490" font-size="11" font-family="monospace">} catch (error) { ... }</text>
                    <text x="820" y="505" font-size="11" font-family="monospace">}</text>
                </g>

                <!-- Arrow markers -->
                <defs>
                    <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#28a745"/>
                    </marker>
                    <marker id="redArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#dc3545"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🏗️ Promise Fundamentals and Creation</div>

        <div class="basics-section">
            <h2>Understanding Promise Basics and Constructor</h2>

            <div class="dual-column">
                <div>
                    <h3>🎯 Promise Constructor</h3>
                    <div class="code-block">
<span class="keyword">const</span> myPromise = <span class="keyword">new</span> <span class="function">Promise</span>((resolve, reject) => {
    <span class="comment">// Executor function runs immediately</span>
    <span class="keyword">const</span> success = <span class="keyword">true</span>;

    <span class="keyword">if</span> (success) {
        <span class="function">resolve</span>(<span class="string">"Operation successful!"</span>);
    } <span class="keyword">else</span> {
        <span class="function">reject</span>(<span class="keyword">new</span> <span class="function">Error</span>(<span class="string">"Operation failed!"</span>));
    }
});
                    </div>

                    <h3>📊 Promise States</h3>
                    <ul>
                        <li><span class="state-indicator pending">PENDING</span> Initial state, operation ongoing</li>
                        <li><span class="state-indicator fulfilled">FULFILLED</span> Operation completed successfully</li>
                        <li><span class="state-indicator rejected">REJECTED</span> Operation failed with an error</li>
                    </ul>
                </div>
                <div>
                    <h3>⚡ Static Promise Methods</h3>
                    <div class="code-block">
<span class="comment">// Immediately resolved promise</span>
<span class="keyword">const</span> resolved = Promise.<span class="function">resolve</span>(<span class="string">"Success!"</span>);

<span class="comment">// Immediately rejected promise</span>
<span class="keyword">const</span> rejected = Promise.<span class="function">reject</span>(<span class="string">"Error!"</span>);

<span class="comment">// Promise from value</span>
<span class="keyword">const</span> fromValue = Promise.<span class="function">resolve</span>(42);

<span class="comment">// Promise from another promise</span>
<span class="keyword">const</span> fromPromise = Promise.<span class="function">resolve</span>(myPromise);
                    </div>

                    <h3>🔄 Promise Characteristics</h3>
                    <ul>
                        <li><strong>Immutable:</strong> State cannot change once settled</li>
                        <li><strong>Eager:</strong> Executor runs immediately</li>
                        <li><strong>Thenable:</strong> Can be chained with .then()</li>
                        <li><strong>Catchable:</strong> Errors can be handled with .catch()</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="interactive-demo">
            <h3>🎮 Interactive Promise Demo</h3>
            <p>Click the buttons below to see different Promise behaviors in action:</p>

            <button class="demo-button" onclick="demoResolvedPromise()">Create Resolved Promise</button>
            <button class="demo-button" onclick="demoRejectedPromise()">Create Rejected Promise</button>
            <button class="demo-button" onclick="demoAsyncPromise()">Create Async Promise</button>
            <button class="demo-button" onclick="clearOutput()">Clear Output</button>

            <div class="demo-output" id="demoOutput">Click a button to see Promise behavior...</div>
        </div>

        <div class="section-title">🔗 Promise Chaining and Methods</div>

        <div class="methods-section">
            <h2>Promise Methods and Chaining Patterns</h2>

            <div class="dual-column">
                <div>
                    <h3>🔗 Basic Chaining</h3>
                    <div class="code-block">
promise
    .<span class="function">then</span>(result => {
        <span class="comment">// Handle success</span>
        console.<span class="function">log</span>(result);
        <span class="keyword">return</span> result * 2;
    })
    .<span class="function">then</span>(doubled => {
        <span class="comment">// Chain another operation</span>
        console.<span class="function">log</span>(doubled);
        <span class="keyword">return</span> doubled + 10;
    })
    .<span class="function">catch</span>(error => {
        <span class="comment">// Handle any error in the chain</span>
        console.<span class="function">error</span>(error);
    })
    .<span class="function">finally</span>(() => {
        <span class="comment">// Always executes</span>
        console.<span class="function">log</span>(<span class="string">"Cleanup"</span>);
    });
                    </div>
                </div>
                <div>
                    <h3>🎯 Error Handling</h3>
                    <div class="code-block">
promise
    .<span class="function">then</span>(result => {
        <span class="keyword">if</span> (!result) {
            <span class="keyword">throw</span> <span class="keyword">new</span> <span class="function">Error</span>(<span class="string">"Invalid result"</span>);
        }
        <span class="keyword">return</span> result;
    })
    .<span class="function">catch</span>(error => {
        <span class="comment">// Handle specific errors</span>
        <span class="keyword">if</span> (error.message === <span class="string">"Invalid result"</span>) {
            <span class="keyword">return</span> <span class="string">"default value"</span>;
        }
        <span class="keyword">throw</span> error; <span class="comment">// Re-throw if not handled</span>
    });
                    </div>
                </div>
            </div>
        </div>

        <div class="section-title">📋 Promise Methods Comparison</div>

        <table>
            <thead>
                <tr>
                    <th>Method</th>
                    <th>Purpose</th>
                    <th>Return Value</th>
                    <th>Error Behavior</th>
                    <th>Use Case</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>.then()</strong></td>
                    <td>Handle fulfilled promises</td>
                    <td>New Promise</td>
                    <td>Passes errors down chain</td>
                    <td>Transform and chain operations</td>
                </tr>
                <tr>
                    <td><strong>.catch()</strong></td>
                    <td>Handle rejected promises</td>
                    <td>New Promise</td>
                    <td>Catches and can recover</td>
                    <td>Error handling and recovery</td>
                </tr>
                <tr>
                    <td><strong>.finally()</strong></td>
                    <td>Cleanup operations</td>
                    <td>Original Promise</td>
                    <td>Always executes</td>
                    <td>Resource cleanup</td>
                </tr>
                <tr>
                    <td><strong>Promise.all()</strong></td>
                    <td>Wait for all promises</td>
                    <td>Array of results</td>
                    <td>Fails if any promise fails</td>
                    <td>Parallel operations</td>
                </tr>
                <tr>
                    <td><strong>Promise.race()</strong></td>
                    <td>First settled promise</td>
                    <td>First result/error</td>
                    <td>Adopts first settlement</td>
                    <td>Timeout implementations</td>
                </tr>
                <tr>
                    <td><strong>Promise.allSettled()</strong></td>
                    <td>Wait for all to settle</td>
                    <td>Array of outcomes</td>
                    <td>Never rejects</td>
                    <td>Batch operations with results</td>
                </tr>
            </tbody>
        </table>

        <script>
            function demoResolvedPromise() {
                const output = document.getElementById('demoOutput');
                output.innerHTML = 'Creating resolved promise...\n';

                const promise = Promise.resolve('Hello, World!');
                promise.then(result => {
                    output.innerHTML += `✅ Promise resolved with: "${result}"\n`;
                    output.innerHTML += `📊 Promise state: FULFILLED\n`;
                });
            }

            function demoRejectedPromise() {
                const output = document.getElementById('demoOutput');
                output.innerHTML = 'Creating rejected promise...\n';

                const promise = Promise.reject(new Error('Something went wrong!'));
                promise.catch(error => {
                    output.innerHTML += `❌ Promise rejected with: "${error.message}"\n`;
                    output.innerHTML += `📊 Promise state: REJECTED\n`;
                });
            }

            function demoAsyncPromise() {
                const output = document.getElementById('demoOutput');
                output.innerHTML = 'Creating async promise...\n';
                output.innerHTML += '⏳ Promise state: PENDING\n';

                const promise = new Promise((resolve) => {
                    setTimeout(() => {
                        resolve('Async operation completed!');
                    }, 2000);
                });

                promise.then(result => {
                    output.innerHTML += `✅ Promise resolved with: "${result}"\n`;
                    output.innerHTML += `📊 Promise state: FULFILLED\n`;
                });
            }

            function clearOutput() {
                document.getElementById('demoOutput').innerHTML = 'Output cleared. Click a button to see Promise behavior...';
            }
        </script>

        <div class="section-title">🚀 Advanced Promise Patterns</div>

        <div class="advanced-section">
            <h2>Advanced Promise Techniques and Patterns</h2>

            <div class="promise-grid">
                <div class="promise-card">
                    <h3>🔄 Promise.all() - Parallel Execution</h3>
                    <div class="code-block">
<span class="keyword">const</span> promises = [
    <span class="function">fetch</span>(<span class="string">'/api/user'</span>),
    <span class="function">fetch</span>(<span class="string">'/api/posts'</span>),
    <span class="function">fetch</span>(<span class="string">'/api/comments'</span>)
];

Promise.<span class="function">all</span>(promises)
    .<span class="function">then</span>(responses => {
        <span class="comment">// All requests completed</span>
        console.<span class="function">log</span>(<span class="string">'All data loaded'</span>);
        <span class="keyword">return</span> Promise.<span class="function">all</span>(
            responses.<span class="function">map</span>(r => r.<span class="function">json</span>())
        );
    })
    .<span class="function">then</span>(data => {
        <span class="comment">// Process all JSON data</span>
        <span class="keyword">const</span> [user, posts, comments] = data;
    })
    .<span class="function">catch</span>(error => {
        <span class="comment">// If ANY promise fails</span>
        console.<span class="function">error</span>(<span class="string">'Failed to load data'</span>);
    });
                    </div>
                    <p><strong>Use Case:</strong> When you need all operations to complete successfully before proceeding.</p>
                </div>

                <div class="promise-card">
                    <h3>🏃 Promise.race() - First to Finish</h3>
                    <div class="code-block">
<span class="keyword">const</span> timeout = <span class="keyword">new</span> <span class="function">Promise</span>((_, reject) => {
    <span class="function">setTimeout</span>(() => {
        <span class="function">reject</span>(<span class="keyword">new</span> <span class="function">Error</span>(<span class="string">'Timeout'</span>));
    }, 5000);
});

<span class="keyword">const</span> apiCall = <span class="function">fetch</span>(<span class="string">'/api/data'</span>);

Promise.<span class="function">race</span>([apiCall, timeout])
    .<span class="function">then</span>(response => {
        <span class="comment">// API call completed first</span>
        <span class="keyword">return</span> response.<span class="function">json</span>();
    })
    .<span class="function">catch</span>(error => {
        <span class="comment">// Either API failed or timeout</span>
        console.<span class="function">error</span>(<span class="string">'Request failed or timed out'</span>);
    });
                    </div>
                    <p><strong>Use Case:</strong> Implementing timeouts or getting the fastest response from multiple sources.</p>
                </div>

                <div class="promise-card">
                    <h3>📊 Promise.allSettled() - All Results</h3>
                    <div class="code-block">
<span class="keyword">const</span> promises = [
    Promise.<span class="function">resolve</span>(<span class="string">'Success 1'</span>),
    Promise.<span class="function">reject</span>(<span class="string">'Error 1'</span>),
    Promise.<span class="function">resolve</span>(<span class="string">'Success 2'</span>)
];

Promise.<span class="function">allSettled</span>(promises)
    .<span class="function">then</span>(results => {
        results.<span class="function">forEach</span>((result, index) => {
            <span class="keyword">if</span> (result.status === <span class="string">'fulfilled'</span>) {
                console.<span class="function">log</span>(`${index}: ${result.value}`);
            } <span class="keyword">else</span> {
                console.<span class="function">error</span>(`${index}: ${result.reason}`);
            }
        });
    });
                    </div>
                    <p><strong>Use Case:</strong> When you want results from all promises regardless of success/failure.</p>
                </div>

                <div class="promise-card">
                    <h3>⚡ Async/Await Syntax</h3>
                    <div class="code-block">
<span class="keyword">async function</span> <span class="function">fetchUserData</span>(userId) {
    <span class="keyword">try</span> {
        <span class="comment">// Sequential execution</span>
        <span class="keyword">const</span> user = <span class="keyword">await</span> <span class="function">fetch</span>(`/api/user/${userId}`);
        <span class="keyword">const</span> userData = <span class="keyword">await</span> user.<span class="function">json</span>();

        <span class="comment">// Parallel execution</span>
        <span class="keyword">const</span> [posts, comments] = <span class="keyword">await</span> Promise.<span class="function">all</span>([
            <span class="function">fetch</span>(`/api/posts/${userId}`),
            <span class="function">fetch</span>(`/api/comments/${userId}`)
        ]);

        <span class="keyword">return</span> {
            user: userData,
            posts: <span class="keyword">await</span> posts.<span class="function">json</span>(),
            comments: <span class="keyword">await</span> comments.<span class="function">json</span>()
        };
    } <span class="keyword">catch</span> (error) {
        console.<span class="function">error</span>(<span class="string">'Error fetching user data:'</span>, error);
        <span class="keyword">throw</span> error;
    }
}
                    </div>
                    <p><strong>Use Case:</strong> Writing asynchronous code that looks and behaves like synchronous code.</p>
                </div>

                <div class="promise-card">
                    <h3>🔄 Promise Chaining Patterns</h3>
                    <div class="code-block">
<span class="comment">// Complex chaining with error recovery</span>
<span class="function">fetchData</span>()
    .<span class="function">then</span>(data => <span class="function">processData</span>(data))
    .<span class="function">then</span>(processed => <span class="function">validateData</span>(processed))
    .<span class="function">catch</span>(error => {
        <span class="comment">// Attempt recovery</span>
        <span class="keyword">if</span> (error.code === <span class="string">'VALIDATION_ERROR'</span>) {
            <span class="keyword">return</span> <span class="function">getDefaultData</span>();
        }
        <span class="keyword">throw</span> error; <span class="comment">// Re-throw if can't recover</span>
    })
    .<span class="function">then</span>(finalData => <span class="function">saveData</span>(finalData))
    .<span class="function">finally</span>(() => {
        <span class="comment">// Cleanup regardless of outcome</span>
        <span class="function">hideLoadingSpinner</span>();
    });
                    </div>
                    <p><strong>Use Case:</strong> Complex data processing pipelines with error recovery.</p>
                </div>

                <div class="promise-card">
                    <h3>🎯 Custom Promise Utilities</h3>
                    <div class="code-block">
<span class="comment">// Delay utility</span>
<span class="keyword">const</span> <span class="function">delay</span> = (ms) => <span class="keyword">new</span> <span class="function">Promise</span>(resolve =>
    <span class="function">setTimeout</span>(resolve, ms)
);

<span class="comment">// Retry utility</span>
<span class="keyword">async function</span> <span class="function">retry</span>(fn, maxAttempts = 3) {
    <span class="keyword">for</span> (<span class="keyword">let</span> attempt = 1; attempt <= maxAttempts; attempt++) {
        <span class="keyword">try</span> {
            <span class="keyword">return</span> <span class="keyword">await</span> <span class="function">fn</span>();
        } <span class="keyword">catch</span> (error) {
            <span class="keyword">if</span> (attempt === maxAttempts) <span class="keyword">throw</span> error;
            <span class="keyword">await</span> <span class="function">delay</span>(1000 * attempt);
        }
    }
}

<span class="comment">// Usage</span>
<span class="keyword">await</span> <span class="function">retry</span>(() => <span class="function">fetch</span>(<span class="string">'/api/data'</span>));
                    </div>
                    <p><strong>Use Case:</strong> Building reusable utilities for common async patterns.</p>
                </div>
            </div>
        </div>

        <div class="section-title">🛠️ Real-World Promise Applications</div>

        <div class="interactive-demo">
            <h3>🎮 Advanced Promise Demos</h3>
            <p>Explore advanced Promise patterns with these interactive examples:</p>

            <button class="demo-button" onclick="demoPromiseAll()">Promise.all() Demo</button>
            <button class="demo-button" onclick="demoPromiseRace()">Promise.race() Demo</button>
            <button class="demo-button" onclick="demoAsyncAwait()">Async/Await Demo</button>
            <button class="demo-button" onclick="demoErrorHandling()">Error Handling Demo</button>
            <button class="demo-button" onclick="clearAdvancedOutput()">Clear Output</button>

            <div class="demo-output" id="advancedDemoOutput">Click a button to see advanced Promise patterns...</div>
        </div>

        <div class="section-title">📚 Promise Best Practices and Common Pitfalls</div>

        <div class="promise-grid">
            <div class="promise-card">
                <h3>✅ Best Practices</h3>
                <ul>
                    <li><strong>Always handle errors:</strong> Use .catch() or try/catch with async/await</li>
                    <li><strong>Avoid nested promises:</strong> Use chaining or async/await instead</li>
                    <li><strong>Return promises in chains:</strong> Don't break the chain</li>
                    <li><strong>Use Promise.all() for parallel operations:</strong> Don't await in loops</li>
                    <li><strong>Prefer async/await:</strong> More readable than .then() chains</li>
                    <li><strong>Handle finally cleanup:</strong> Use .finally() for cleanup operations</li>
                </ul>
            </div>

            <div class="promise-card">
                <h3>❌ Common Pitfalls</h3>
                <ul>
                    <li><strong>Forgetting to return:</strong> Breaking promise chains</li>
                    <li><strong>Nested promises:</strong> Creating "promise hell"</li>
                    <li><strong>Not handling errors:</strong> Unhandled promise rejections</li>
                    <li><strong>Sequential instead of parallel:</strong> Unnecessary waiting</li>
                    <li><strong>Mixing callbacks and promises:</strong> Inconsistent patterns</li>
                    <li><strong>Not using async/await:</strong> Missing cleaner syntax</li>
                </ul>
            </div>

            <div class="promise-card">
                <h3>🔧 Performance Tips</h3>
                <ul>
                    <li><strong>Use Promise.all():</strong> For independent parallel operations</li>
                    <li><strong>Avoid unnecessary awaits:</strong> Don't await if you don't need the result immediately</li>
                    <li><strong>Cache promises:</strong> Don't create duplicate promises for same operation</li>
                    <li><strong>Use Promise.allSettled():</strong> When you need all results regardless of failures</li>
                    <li><strong>Implement timeouts:</strong> Use Promise.race() with timeout promises</li>
                    <li><strong>Consider streaming:</strong> For large data sets, use streams instead of promises</li>
                </ul>
            </div>
        </div>

        <div class="section-title">📋 Promise vs Callback vs Async/Await Comparison</div>

        <table>
            <thead>
                <tr>
                    <th>Aspect</th>
                    <th>Callbacks</th>
                    <th>Promises</th>
                    <th>Async/Await</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Readability</strong></td>
                    <td>Poor (callback hell)</td>
                    <td>Good (chainable)</td>
                    <td>Excellent (synchronous-like)</td>
                </tr>
                <tr>
                    <td><strong>Error Handling</strong></td>
                    <td>Manual error checking</td>
                    <td>.catch() method</td>
                    <td>try/catch blocks</td>
                </tr>
                <tr>
                    <td><strong>Composition</strong></td>
                    <td>Difficult</td>
                    <td>Good with .then()</td>
                    <td>Excellent</td>
                </tr>
                <tr>
                    <td><strong>Debugging</strong></td>
                    <td>Challenging</td>
                    <td>Better stack traces</td>
                    <td>Best debugging experience</td>
                </tr>
                <tr>
                    <td><strong>Parallel Execution</strong></td>
                    <td>Complex coordination</td>
                    <td>Promise.all(), Promise.race()</td>
                    <td>await Promise.all()</td>
                </tr>
                <tr>
                    <td><strong>Browser Support</strong></td>
                    <td>Universal</td>
                    <td>ES6+ (polyfillable)</td>
                    <td>ES2017+ (transpilable)</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">📚 Summary: Mastering JavaScript Promises</div>

        <div class="overview-section">
            <h2>Transforming Asynchronous JavaScript Development</h2>
            <p>JavaScript Promises have revolutionized asynchronous programming by providing a clean, composable, and powerful alternative to callback-based patterns. From basic Promise creation and chaining to advanced patterns with async/await, Promises enable developers to write more maintainable, readable, and robust asynchronous code. Understanding Promise fundamentals, mastering chaining patterns, and leveraging modern async/await syntax are essential skills for modern JavaScript development.</p>

            <div class="dual-column">
                <div>
                    <h3>🔑 Key Concepts Mastered:</h3>
                    <ul>
                        <li><strong>Promise States:</strong> Pending, Fulfilled, and Rejected lifecycle</li>
                        <li><strong>Promise Creation:</strong> Constructor pattern and static methods</li>
                        <li><strong>Chaining:</strong> .then(), .catch(), and .finally() methods</li>
                        <li><strong>Parallel Execution:</strong> Promise.all(), Promise.race(), Promise.allSettled()</li>
                        <li><strong>Modern Syntax:</strong> async/await for cleaner asynchronous code</li>
                        <li><strong>Error Handling:</strong> Robust error management strategies</li>
                    </ul>
                </div>
                <div>
                    <h3>🎯 Practical Applications:</h3>
                    <ul>
                        <li>API calls and data fetching</li>
                        <li>File operations and I/O</li>
                        <li>Database operations</li>
                        <li>Image and resource loading</li>
                        <li>Animation and timing</li>
                        <li>Complex workflow orchestration</li>
                    </ul>
                </div>
            </div>

            <p><strong>The future of asynchronous JavaScript</strong> continues to evolve with new proposals like top-level await, async iterators, and enhanced error handling, making Promises an even more powerful tool for modern web development.</p>
        </div>

        <script>
            function demoPromiseAll() {
                const output = document.getElementById('advancedDemoOutput');
                output.innerHTML = 'Demonstrating Promise.all() with simulated API calls...\n';

                const promises = [
                    new Promise(resolve => setTimeout(() => resolve('User data loaded'), 1000)),
                    new Promise(resolve => setTimeout(() => resolve('Posts loaded'), 1500)),
                    new Promise(resolve => setTimeout(() => resolve('Comments loaded'), 800))
                ];

                const startTime = Date.now();
                Promise.all(promises).then(results => {
                    const endTime = Date.now();
                    output.innerHTML += `✅ All promises resolved in ${endTime - startTime}ms:\n`;
                    results.forEach((result, index) => {
                        output.innerHTML += `  ${index + 1}. ${result}\n`;
                    });
                });
            }

            function demoPromiseRace() {
                const output = document.getElementById('advancedDemoOutput');
                output.innerHTML = 'Demonstrating Promise.race() with timeout...\n';

                const apiCall = new Promise(resolve =>
                    setTimeout(() => resolve('API response received'), 2000)
                );
                const timeout = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Request timeout')), 1500)
                );

                Promise.race([apiCall, timeout])
                    .then(result => {
                        output.innerHTML += `✅ Race won by: ${result}\n`;
                    })
                    .catch(error => {
                        output.innerHTML += `❌ Race lost: ${error.message}\n`;
                    });
            }

            async function demoAsyncAwait() {
                const output = document.getElementById('advancedDemoOutput');
                output.innerHTML = 'Demonstrating async/await pattern...\n';

                try {
                    output.innerHTML += '⏳ Step 1: Fetching user...\n';
                    await new Promise(resolve => setTimeout(resolve, 500));
                    output.innerHTML += '✅ Step 1: User fetched\n';

                    output.innerHTML += '⏳ Step 2: Fetching preferences...\n';
                    await new Promise(resolve => setTimeout(resolve, 300));
                    output.innerHTML += '✅ Step 2: Preferences fetched\n';

                    output.innerHTML += '⏳ Step 3: Processing data...\n';
                    await new Promise(resolve => setTimeout(resolve, 400));
                    output.innerHTML += '✅ Step 3: Data processed\n';

                    output.innerHTML += '🎉 All steps completed successfully!\n';
                } catch (error) {
                    output.innerHTML += `❌ Error: ${error.message}\n`;
                }
            }

            function demoErrorHandling() {
                const output = document.getElementById('advancedDemoOutput');
                output.innerHTML = 'Demonstrating error handling patterns...\n';

                const flakyPromise = new Promise((resolve, reject) => {
                    setTimeout(() => {
                        if (Math.random() > 0.5) {
                            resolve('Operation successful!');
                        } else {
                            reject(new Error('Random failure occurred'));
                        }
                    }, 1000);
                });

                flakyPromise
                    .then(result => {
                        output.innerHTML += `✅ Success: ${result}\n`;
                    })
                    .catch(error => {
                        output.innerHTML += `❌ Caught error: ${error.message}\n`;
                        output.innerHTML += '🔄 Attempting recovery...\n';
                        return 'Recovered with default value';
                    })
                    .then(finalResult => {
                        output.innerHTML += `📊 Final result: ${finalResult}\n`;
                    })
                    .finally(() => {
                        output.innerHTML += '🧹 Cleanup completed\n';
                    });
            }

            function clearAdvancedOutput() {
                document.getElementById('advancedDemoOutput').innerHTML = 'Output cleared. Click a button to see advanced Promise patterns...';
            }
        </script>
    </div>
</body>
</html>
