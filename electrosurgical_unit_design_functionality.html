<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electrosurgical Unit Design and Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .design-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .functionality-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        .safety-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Electrosurgical Unit Design and Functionality</h1>

        <div class="overview-section">
            <h2>Advanced Engineering for Surgical Excellence</h2>
            <p>Electrosurgical units (ESUs) represent sophisticated medical devices that combine advanced electrical engineering, precise control systems, and comprehensive safety mechanisms to deliver high-frequency electrical energy for surgical applications. Modern ESU design encompasses complex circuitry, intelligent feedback systems, user-friendly interfaces, and multiple safety features that ensure optimal performance while maintaining the highest standards of patient and operator safety. Understanding ESU design and functionality is crucial for effective utilization, maintenance, and troubleshooting of these critical surgical instruments.</p>
        </div>

        <div class="section-title">🔧 ESU System Architecture</div>

        <div class="diagram-container">
            <svg width="100%" height="900" viewBox="0 0 1200 900">
                <!-- ESU System Architecture Diagram -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">ELECTROSURGICAL UNIT - SYSTEM ARCHITECTURE</text>

                <!-- Power supply section -->
                <g>
                    <text x="200" y="80" text-anchor="middle" font-size="16" font-weight="bold">POWER SUPPLY SECTION</text>

                    <!-- AC input -->
                    <rect x="100" y="100" width="80" height="60" fill="#ffeb3b" stroke="#f57c00" stroke-width="3"/>
                    <text x="140" y="125" text-anchor="middle" font-size="12" font-weight="bold">AC INPUT</text>
                    <text x="140" y="140" text-anchor="middle" font-size="10">110/220V</text>
                    <text x="140" y="155" text-anchor="middle" font-size="10">50/60 Hz</text>

                    <!-- Isolation transformer -->
                    <rect x="200" y="100" width="100" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="250" y="120" text-anchor="middle" font-size="12" font-weight="bold">ISOLATION</text>
                    <text x="250" y="135" text-anchor="middle" font-size="12" font-weight="bold">TRANSFORMER</text>
                    <text x="250" y="155" text-anchor="middle" font-size="10">Patient Safety</text>

                    <!-- DC power supply -->
                    <rect x="320" y="100" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="370" y="120" text-anchor="middle" font-size="12" font-weight="bold">DC POWER</text>
                    <text x="370" y="135" text-anchor="middle" font-size="12" font-weight="bold">SUPPLY</text>
                    <text x="370" y="155" text-anchor="middle" font-size="10">±15V, ±5V</text>

                    <!-- Power flow -->
                    <path d="M 180 130 L 200 130" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 300 130 L 320 130" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                </g>

                <!-- RF generation section -->
                <g>
                    <text x="600" y="80" text-anchor="middle" font-size="16" font-weight="bold">RF GENERATION SECTION</text>

                    <!-- Oscillator -->
                    <rect x="500" y="100" width="100" height="60" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="550" y="120" text-anchor="middle" font-size="12" font-weight="bold">RF OSCILLATOR</text>
                    <text x="550" y="135" text-anchor="middle" font-size="10">300kHz-3MHz</text>
                    <text x="550" y="155" text-anchor="middle" font-size="10">Crystal Control</text>

                    <!-- Amplifier -->
                    <rect x="620" y="100" width="100" height="60" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="670" y="120" text-anchor="middle" font-size="12" font-weight="bold">RF AMPLIFIER</text>
                    <text x="670" y="135" text-anchor="middle" font-size="10">Power Stage</text>
                    <text x="670" y="155" text-anchor="middle" font-size="10">0-400W</text>

                    <!-- Waveform generator -->
                    <rect x="740" y="100" width="100" height="60" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="790" y="115" text-anchor="middle" font-size="12" font-weight="bold">WAVEFORM</text>
                    <text x="790" y="130" text-anchor="middle" font-size="12" font-weight="bold">GENERATOR</text>
                    <text x="790" y="145" text-anchor="middle" font-size="10">Cut/Coag</text>
                    <text x="790" y="160" text-anchor="middle" font-size="10">Modulation</text>

                    <!-- RF flow -->
                    <path d="M 600 130 L 620 130" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 720 130 L 740 130" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                </g>

                <!-- Control and monitoring -->
                <g>
                    <text x="1000" y="80" text-anchor="middle" font-size="16" font-weight="bold">CONTROL & MONITORING</text>

                    <!-- Microprocessor -->
                    <rect x="900" y="100" width="100" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="3"/>
                    <text x="950" y="115" text-anchor="middle" font-size="12" font-weight="bold">MICRO-</text>
                    <text x="950" y="130" text-anchor="middle" font-size="12" font-weight="bold">PROCESSOR</text>
                    <text x="950" y="145" text-anchor="middle" font-size="10">Control Logic</text>
                    <text x="950" y="160" text-anchor="middle" font-size="10">Safety Monitor</text>

                    <!-- User interface -->
                    <rect x="1020" y="100" width="100" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="3"/>
                    <text x="1070" y="115" text-anchor="middle" font-size="12" font-weight="bold">USER</text>
                    <text x="1070" y="130" text-anchor="middle" font-size="12" font-weight="bold">INTERFACE</text>
                    <text x="1070" y="145" text-anchor="middle" font-size="10">Display</text>
                    <text x="1070" y="160" text-anchor="middle" font-size="10">Controls</text>

                    <!-- Control flow -->
                    <path d="M 1000 130 L 1020 130" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                </g>

                <!-- Output section -->
                <g>
                    <text x="600" y="220" text-anchor="middle" font-size="16" font-weight="bold">OUTPUT SECTION</text>

                    <!-- Output transformer -->
                    <rect x="450" y="240" width="120" height="80" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="510" y="265" text-anchor="middle" font-size="12" font-weight="bold">OUTPUT</text>
                    <text x="510" y="280" text-anchor="middle" font-size="12" font-weight="bold">TRANSFORMER</text>
                    <text x="510" y="295" text-anchor="middle" font-size="10">Impedance</text>
                    <text x="510" y="310" text-anchor="middle" font-size="10">Matching</text>

                    <!-- Active output -->
                    <rect x="600" y="240" width="100" height="80" fill="#ffeb3b" stroke="#f57c00" stroke-width="3"/>
                    <text x="650" y="265" text-anchor="middle" font-size="12" font-weight="bold">ACTIVE</text>
                    <text x="650" y="280" text-anchor="middle" font-size="12" font-weight="bold">OUTPUT</text>
                    <text x="650" y="295" text-anchor="middle" font-size="10">Monopolar</text>
                    <text x="650" y="310" text-anchor="middle" font-size="10">Bipolar</text>

                    <!-- Return electrode monitoring -->
                    <rect x="730" y="240" width="120" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="790" y="260" text-anchor="middle" font-size="12" font-weight="bold">RETURN</text>
                    <text x="790" y="275" text-anchor="middle" font-size="12" font-weight="bold">ELECTRODE</text>
                    <text x="790" y="290" text-anchor="middle" font-size="12" font-weight="bold">MONITORING</text>
                    <text x="790" y="305" text-anchor="middle" font-size="10">Contact Quality</text>

                    <!-- Output connections -->
                    <path d="M 570 280 L 600 280" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 700 280 L 730 280" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                </g>

                <!-- Safety systems -->
                <g>
                    <text x="300" y="380" text-anchor="middle" font-size="16" font-weight="bold">SAFETY SYSTEMS</text>

                    <!-- Isolation monitoring -->
                    <rect x="200" y="400" width="100" height="60" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="250" y="420" text-anchor="middle" font-size="12" font-weight="bold">ISOLATION</text>
                    <text x="250" y="435" text-anchor="middle" font-size="12" font-weight="bold">MONITOR</text>
                    <text x="250" y="455" text-anchor="middle" font-size="10">Ground Fault</text>

                    <!-- Leakage current test -->
                    <rect x="320" y="400" width="100" height="60" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="370" y="415" text-anchor="middle" font-size="12" font-weight="bold">LEAKAGE</text>
                    <text x="370" y="430" text-anchor="middle" font-size="12" font-weight="bold">CURRENT</text>
                    <text x="370" y="445" text-anchor="middle" font-size="12" font-weight="bold">TEST</text>
                    <text x="370" y="460" text-anchor="middle" font-size="10"><10mA</text>

                    <!-- Emergency stop -->
                    <rect x="440" y="400" width="100" height="60" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="490" y="420" text-anchor="middle" font-size="12" font-weight="bold">EMERGENCY</text>
                    <text x="490" y="435" text-anchor="middle" font-size="12" font-weight="bold">STOP</text>
                    <text x="490" y="455" text-anchor="middle" font-size="10">Immediate</text>
                </g>

                <!-- Feedback systems -->
                <g>
                    <text x="900" y="380" text-anchor="middle" font-size="16" font-weight="bold">FEEDBACK SYSTEMS</text>

                    <!-- Power measurement -->
                    <rect x="800" y="400" width="100" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="850" y="420" text-anchor="middle" font-size="12" font-weight="bold">POWER</text>
                    <text x="850" y="435" text-anchor="middle" font-size="12" font-weight="bold">MEASUREMENT</text>
                    <text x="850" y="455" text-anchor="middle" font-size="10">Real-time</text>

                    <!-- Impedance monitoring -->
                    <rect x="920" y="400" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="970" y="415" text-anchor="middle" font-size="12" font-weight="bold">IMPEDANCE</text>
                    <text x="970" y="430" text-anchor="middle" font-size="12" font-weight="bold">MONITORING</text>
                    <text x="970" y="450" text-anchor="middle" font-size="10">Tissue Load</text>

                    <!-- Temperature sensing -->
                    <rect x="1040" y="400" width="100" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="3"/>
                    <text x="1090" y="415" text-anchor="middle" font-size="12" font-weight="bold">TEMPERATURE</text>
                    <text x="1090" y="430" text-anchor="middle" font-size="12" font-weight="bold">SENSING</text>
                    <text x="1090" y="450" text-anchor="middle" font-size="10">Thermal Safety</text>
                </g>

                <!-- Patient circuit -->
                <g>
                    <text x="600" y="520" text-anchor="middle" font-size="16" font-weight="bold">PATIENT CIRCUIT</text>

                    <!-- Active electrode -->
                    <rect x="400" y="540" width="100" height="60" fill="#ffeb3b" stroke="#f57c00" stroke-width="3"/>
                    <text x="450" y="565" text-anchor="middle" font-size="12" font-weight="bold">ACTIVE</text>
                    <text x="450" y="580" text-anchor="middle" font-size="12" font-weight="bold">ELECTRODE</text>
                    <text x="450" y="595" text-anchor="middle" font-size="10">High Density</text>

                    <!-- Patient -->
                    <ellipse cx="600" cy="570" rx="80" ry="40" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="600" y="575" text-anchor="middle" font-size="14" font-weight="bold">PATIENT</text>
                    <text x="600" y="590" text-anchor="middle" font-size="11">Tissue Load</text>

                    <!-- Return electrode -->
                    <rect x="700" y="540" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="750" y="565" text-anchor="middle" font-size="12" font-weight="bold">RETURN</text>
                    <text x="750" y="580" text-anchor="middle" font-size="12" font-weight="bold">ELECTRODE</text>
                    <text x="750" y="595" text-anchor="middle" font-size="10">Low Density</text>

                    <!-- Current flow -->
                    <path d="M 500 570 L 520 570" stroke="#ff5722" stroke-width="4" fill="none" marker-end="url(#arrow2)"/>
                    <path d="M 680 570 L 700 570" stroke="#ff5722" stroke-width="4" fill="none" marker-end="url(#arrow2)"/>
                    <text x="510" y="560" text-anchor="middle" font-size="10" fill="#ff5722">Current</text>
                    <text x="690" y="560" text-anchor="middle" font-size="10" fill="#ff5722">Current</text>
                </g>

                <!-- Control connections -->
                <path d="M 950 160 L 950 200 L 790 200 L 790 240" stroke="#4caf50" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <path d="M 950 200 L 510 200 L 510 240" stroke="#4caf50" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <text x="730" y="215" text-anchor="middle" font-size="10" fill="#4caf50">Control Signals</text>

                <!-- Feedback connections -->
                <path d="M 850 400 L 850 350 L 650 350 L 650 320" stroke="#2196f3" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <path d="M 970 400 L 970 350 L 790 350 L 790 320" stroke="#2196f3" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <text x="810" y="365" text-anchor="middle" font-size="10" fill="#2196f3">Feedback Signals</text>

                <!-- Specifications -->
                <g>
                    <text x="200" y="680" font-size="14" font-weight="bold">ELECTRICAL SPECIFICATIONS</text>
                    <text x="200" y="700" font-size="12">Input Power: 110/220V AC, 50/60Hz</text>
                    <text x="200" y="715" font-size="12">Output Power: 0-400W (adjustable)</text>
                    <text x="200" y="730" font-size="12">Frequency: 300kHz-3MHz</text>
                    <text x="200" y="745" font-size="12">Isolation: >4000V</text>
                    <text x="200" y="760" font-size="12">Leakage Current: <10mA</text>

                    <text x="600" y="680" font-size="14" font-weight="bold">SAFETY FEATURES</text>
                    <text x="600" y="700" font-size="12">Return Electrode Monitoring</text>
                    <text x="600" y="715" font-size="12">Active Electrode Monitoring</text>
                    <text x="600" y="730" font-size="12">Isolation Monitoring</text>
                    <text x="600" y="745" font-size="12">Emergency Stop System</text>
                    <text x="600" y="760" font-size="12">Automatic Power Control</text>

                    <text x="1000" y="680" font-size="14" font-weight="bold">CONTROL FEATURES</text>
                    <text x="1000" y="700" font-size="12">Microprocessor Control</text>
                    <text x="1000" y="715" font-size="12">Digital Display</text>
                    <text x="1000" y="730" font-size="12">Memory Presets</text>
                    <text x="1000" y="745" font-size="12">Automatic Calibration</text>
                    <text x="1000" y="760" font-size="12">Error Diagnostics</text>
                </g>

                <!-- Arrow markers -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                    <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#ff5722"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🔧 Design Components and Engineering</div>

        <div class="design-section">
            <h2>Advanced Engineering and Component Integration</h2>
            <div class="dual-column">
                <div>
                    <h3>⚡ Power Supply Design</h3>
                    <ul>
                        <li><strong>Isolation Transformer:</strong></li>
                        <ul>
                            <li>Patient electrical isolation</li>
                            <li>Ground fault protection</li>
                            <li>Leakage current limitation</li>
                            <li>Safety compliance (IEC 60601-1)</li>
                        </ul>
                        <li><strong>Switching Power Supply:</strong></li>
                        <ul>
                            <li>High efficiency (>85%)</li>
                            <li>Compact design</li>
                            <li>Multiple voltage outputs</li>
                            <li>Overcurrent protection</li>
                        </ul>
                        <li><strong>Power Factor Correction:</strong></li>
                        <ul>
                            <li>Improved efficiency</li>
                            <li>Reduced harmonics</li>
                            <li>Regulatory compliance</li>
                            <li>Universal input voltage</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>📡 RF Generation Circuit</h3>
                    <ul>
                        <li><strong>Crystal Oscillator:</strong></li>
                        <ul>
                            <li>Frequency stability (±0.01%)</li>
                            <li>Temperature compensation</li>
                            <li>Low phase noise</li>
                            <li>Multiple frequency options</li>
                        </ul>
                        <li><strong>Power Amplifier:</strong></li>
                        <ul>
                            <li>Class D/E topology</li>
                            <li>High efficiency (>90%)</li>
                            <li>Wide bandwidth</li>
                            <li>Thermal protection</li>
                        </ul>
                        <li><strong>Output Matching:</strong></li>
                        <ul>
                            <li>Impedance transformation</li>
                            <li>Load adaptation</li>
                            <li>Harmonic filtering</li>
                            <li>VSWR protection</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">⚙️ Functional Systems and Control</div>

        <div class="functionality-section">
            <h2>Intelligent Control and Monitoring Systems</h2>
            <div class="dual-column">
                <div>
                    <h3>🖥️ Microprocessor Control</h3>
                    <ul>
                        <li><strong>Control Functions:</strong></li>
                        <ul>
                            <li>Power level adjustment</li>
                            <li>Waveform selection</li>
                            <li>Safety monitoring</li>
                            <li>User interface management</li>
                        </ul>
                        <li><strong>Real-time Processing:</strong></li>
                        <ul>
                            <li>Feedback loop control</li>
                            <li>Impedance measurement</li>
                            <li>Power calculation</li>
                            <li>Safety algorithm execution</li>
                        </ul>
                        <li><strong>Memory Functions:</strong></li>
                        <ul>
                            <li>User preference storage</li>
                            <li>Calibration data</li>
                            <li>Error logging</li>
                            <li>Usage statistics</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>📊 Monitoring and Feedback</h3>
                    <ul>
                        <li><strong>Power Measurement:</strong></li>
                        <ul>
                            <li>True RMS calculation</li>
                            <li>Forward/reflected power</li>
                            <li>Efficiency monitoring</li>
                            <li>Load impedance calculation</li>
                        </ul>
                        <li><strong>Safety Monitoring:</strong></li>
                        <ul>
                            <li>Return electrode contact</li>
                            <li>Isolation integrity</li>
                            <li>Temperature monitoring</li>
                            <li>Leakage current detection</li>
                        </ul>
                        <li><strong>Adaptive Control:</strong></li>
                        <ul>
                            <li>Automatic power adjustment</li>
                            <li>Load compensation</li>
                            <li>Tissue impedance tracking</li>
                            <li>Optimal energy delivery</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">🛡️ Safety Systems and Protection</div>

        <div class="safety-section">
            <h2>Comprehensive Safety and Protection Mechanisms</h2>
            <div class="dual-column">
                <div>
                    <h3>⚠️ Patient Safety Systems</h3>
                    <ul>
                        <li><strong>Return Electrode Monitoring (REM):</strong></li>
                        <ul>
                            <li>Dual-plate electrode design</li>
                            <li>Contact quality assessment</li>
                            <li>Impedance measurement</li>
                            <li>Automatic shutdown on fault</li>
                        </ul>
                        <li><strong>Active Electrode Monitoring (AEM):</strong></li>
                        <ul>
                            <li>Insulation integrity testing</li>
                            <li>Capacitive coupling detection</li>
                            <li>Stray current monitoring</li>
                            <li>Real-time safety assessment</li>
                        </ul>
                        <li><strong>Isolation Monitoring:</strong></li>
                        <ul>
                            <li>Ground fault detection</li>
                            <li>Leakage current measurement</li>
                            <li>Isolation resistance testing</li>
                            <li>Continuous safety verification</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>🔒 Equipment Protection</h3>
                    <ul>
                        <li><strong>Thermal Protection:</strong></li>
                        <ul>
                            <li>Temperature sensors</li>
                            <li>Cooling fan control</li>
                            <li>Thermal shutdown</li>
                            <li>Duty cycle limitation</li>
                        </ul>
                        <li><strong>Electrical Protection:</strong></li>
                        <ul>
                            <li>Overcurrent protection</li>
                            <li>Overvoltage protection</li>
                            <li>Short circuit protection</li>
                            <li>VSWR protection</li>
                        </ul>
                        <li><strong>Software Protection:</strong></li>
                        <ul>
                            <li>Watchdog timers</li>
                            <li>Error detection algorithms</li>
                            <li>Safe state defaults</li>
                            <li>Diagnostic routines</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">📋 Technical Specifications and Performance</div>

        <table>
            <thead>
                <tr>
                    <th>System Component</th>
                    <th>Specification</th>
                    <th>Performance Parameter</th>
                    <th>Safety Standard</th>
                    <th>Monitoring Method</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>RF Generator</strong></td>
                    <td>300 kHz - 3 MHz</td>
                    <td>Frequency stability ±0.01%</td>
                    <td>FCC Part 18</td>
                    <td>Frequency counter</td>
                </tr>
                <tr>
                    <td><strong>Power Amplifier</strong></td>
                    <td>0-400W output</td>
                    <td>Efficiency >90%</td>
                    <td>Power limitation</td>
                    <td>True RMS measurement</td>
                </tr>
                <tr>
                    <td><strong>Isolation System</strong></td>
                    <td>>4000V isolation</td>
                    <td>Leakage <10mA</td>
                    <td>IEC 60601-1</td>
                    <td>Hipot testing</td>
                </tr>
                <tr>
                    <td><strong>Return Electrode Monitor</strong></td>
                    <td>Contact impedance <50Ω</td>
                    <td>Response time <1s</td>
                    <td>Automatic shutdown</td>
                    <td>Impedance measurement</td>
                </tr>
                <tr>
                    <td><strong>Control System</strong></td>
                    <td>32-bit microprocessor</td>
                    <td>Response time <10ms</td>
                    <td>Fail-safe operation</td>
                    <td>Self-diagnostics</td>
                </tr>
                <tr>
                    <td><strong>User Interface</strong></td>
                    <td>LCD/LED display</td>
                    <td>Update rate 10Hz</td>
                    <td>Clear visibility</td>
                    <td>Visual inspection</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">🎯 Advanced Features and Technologies</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>🧠 Intelligent Control Systems</h3>
                <ul>
                    <li><strong>Adaptive Power Control:</strong></li>
                    <ul>
                        <li>Tissue impedance sensing</li>
                        <li>Automatic power adjustment</li>
                        <li>Optimal energy delivery</li>
                        <li>Consistent surgical effects</li>
                    </ul>
                    <li><strong>Smart Algorithms:</strong></li>
                    <ul>
                        <li>Predictive control</li>
                        <li>Load compensation</li>
                        <li>Efficiency optimization</li>
                        <li>Safety enhancement</li>
                    </ul>
                    <li><strong>User Assistance:</strong></li>
                    <ul>
                        <li>Guided setup procedures</li>
                        <li>Error diagnostics</li>
                        <li>Maintenance reminders</li>
                        <li>Training modes</li>
                    </ul>
                </ul>
            </div>

            <div class="component-card">
                <h3>📡 Communication and Connectivity</h3>
                <ul>
                    <li><strong>Network Integration:</strong></li>
                    <ul>
                        <li>Ethernet connectivity</li>
                        <li>WiFi capability</li>
                        <li>Hospital information systems</li>
                        <li>Remote monitoring</li>
                    </ul>
                    <li><strong>Data Management:</strong></li>
                    <ul>
                        <li>Usage logging</li>
                        <li>Performance tracking</li>
                        <li>Maintenance scheduling</li>
                        <li>Quality assurance</li>
                    </ul>
                    <li><strong>Integration Features:</strong></li>
                    <ul>
                        <li>OR integration systems</li>
                        <li>Video documentation</li>
                        <li>Surgical navigation</li>
                        <li>Equipment coordination</li>
                    </ul>
                </ul>
            </div>

            <div class="component-card">
                <h3>🔧 Maintenance and Serviceability</h3>
                <ul>
                    <li><strong>Modular Design:</strong></li>
                    <ul>
                        <li>Replaceable components</li>
                        <li>Easy access panels</li>
                        <li>Plug-in modules</li>
                        <li>Reduced downtime</li>
                    </ul>
                    <li><strong>Diagnostic Systems:</strong></li>
                    <ul>
                        <li>Built-in test equipment</li>
                        <li>Automated calibration</li>
                        <li>Performance verification</li>
                        <li>Fault isolation</li>
                    </ul>
                    <li><strong>Service Features:</strong></li>
                    <ul>
                        <li>Remote diagnostics</li>
                        <li>Predictive maintenance</li>
                        <li>Service documentation</li>
                        <li>Training resources</li>
                    </ul>
                </ul>
            </div>
        </div>

        <div class="section-title">📚 Summary: ESU Design Excellence</div>

        <div class="overview-section">
            <h2>Engineering Innovation in Electrosurgical Technology</h2>
            <p>Modern electrosurgical unit design represents the pinnacle of medical device engineering, combining sophisticated electrical circuits, intelligent control systems, and comprehensive safety mechanisms to deliver precise and safe surgical energy. The integration of advanced microprocessor control, real-time monitoring, and adaptive feedback systems ensures optimal performance while maintaining the highest standards of patient and operator safety. Understanding ESU design and functionality is essential for effective utilization, proper maintenance, and continued advancement of electrosurgical technology.</p>

            <div class="dual-column">
                <div>
                    <h3>🔑 Design Excellence:</h3>
                    <ul>
                        <li><strong>Advanced Circuitry:</strong> High-efficiency RF generation and control</li>
                        <li><strong>Intelligent Control:</strong> Microprocessor-based adaptive systems</li>
                        <li><strong>Safety Integration:</strong> Comprehensive protection mechanisms</li>
                        <li><strong>User Interface:</strong> Intuitive operation and feedback</li>
                        <li><strong>Connectivity:</strong> Modern communication and integration</li>
                    </ul>
                </div>
                <div>
                    <h3>🎯 Functional Benefits:</h3>
                    <ul>
                        <li>Precise energy delivery and control</li>
                        <li>Enhanced patient safety and protection</li>
                        <li>Improved surgical efficiency and outcomes</li>
                        <li>Reduced maintenance and downtime</li>
                        <li>Future-ready technology platform</li>
                    </ul>
                </div>
            </div>

            <p><strong>The evolution of ESU design</strong> continues with advances in digital signal processing, artificial intelligence, and smart materials, promising even greater precision, safety, and integration capabilities for future surgical applications.</p>
        </div>
    </div>
</body>
</html>
