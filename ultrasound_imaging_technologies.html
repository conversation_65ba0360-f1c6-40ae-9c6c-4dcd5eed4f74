<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultrasound Imaging Technologies</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .ultrasound-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .ultrasound-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .ultrasound-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .ultrasound-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .physics-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .transducer-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        .doppler-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .formula-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
        }
        .formula-box h4 {
            color: #856404;
            margin-top: 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 Ultrasound Imaging Technologies</h1>

        <div class="overview-section">
            <h2>Advanced Acoustic Imaging and Diagnostic Ultrasound</h2>
            <p>Ultrasound imaging represents one of the most versatile and widely used diagnostic imaging modalities in modern medicine, utilizing high-frequency sound waves to create real-time images of internal body structures. This non-ionizing imaging technology combines sophisticated acoustic physics, advanced transducer design, and powerful signal processing to provide detailed anatomical and functional information across numerous medical specialties. Modern ultrasound encompasses 2D/3D/4D imaging, Doppler flow assessment, contrast-enhanced studies, and specialized applications that continue to expand the diagnostic capabilities while maintaining excellent safety profiles and cost-effectiveness in clinical practice.</p>
        </div>

        <div class="section-title">🔊 Ultrasound System Architecture and Physics</div>

        <div class="diagram-container">
            <svg width="100%" height="900" viewBox="0 0 1200 900">
                <!-- Ultrasound System Architecture Diagram -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">ULTRASOUND IMAGING SYSTEM ARCHITECTURE</text>

                <!-- Transducer -->
                <g>
                    <text x="300" y="80" text-anchor="middle" font-size="16" font-weight="bold">ULTRASOUND TRANSDUCER</text>

                    <!-- Transducer housing -->
                    <rect x="200" y="100" width="200" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="4" rx="15"/>
                    <text x="300" y="90" text-anchor="middle" font-size="12">Transducer Assembly</text>

                    <!-- Piezoelectric elements -->
                    <rect x="220" y="120" width="160" height="20" fill="#ffeb3b" stroke="#f57c00" stroke-width="2"/>
                    <text x="300" y="135" text-anchor="middle" font-size="10" font-weight="bold">PIEZOELECTRIC ELEMENTS</text>

                    <!-- Backing material -->
                    <rect x="220" y="145" width="160" height="15" fill="#9e9e9e" stroke="#616161" stroke-width="2"/>
                    <text x="300" y="157" text-anchor="middle" font-size="9">Backing Material</text>

                    <!-- Matching layers -->
                    <rect x="220" y="165" width="160" height="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                    <text x="300" y="173" text-anchor="middle" font-size="8">Matching Layer</text>

                    <!-- Acoustic lens -->
                    <path d="M 220 180 Q 300 190 380 180 L 380 200 L 220 200 Z" fill="#81d4fa" stroke="#0288d1" stroke-width="2"/>
                    <text x="300" y="195" text-anchor="middle" font-size="9">Acoustic Lens</text>

                    <!-- Ultrasound beam -->
                    <path d="M 280 220 L 270 280" stroke="#9c27b0" stroke-width="3" fill="none"/>
                    <path d="M 300 220 L 300 280" stroke="#9c27b0" stroke-width="4" fill="none"/>
                    <path d="M 320 220 L 330 280" stroke="#9c27b0" stroke-width="3" fill="none"/>
                    <text x="320" y="250" text-anchor="middle" font-size="10" fill="#9c27b0">Ultrasound</text>
                    <text x="320" y="265" text-anchor="middle" font-size="10" fill="#9c27b0">Beam</text>
                </g>

                <!-- Beam forming -->
                <g>
                    <text x="700" y="80" text-anchor="middle" font-size="16" font-weight="bold">BEAM FORMING</text>

                    <!-- Transmit beamformer -->
                    <rect x="600" y="100" width="100" height="60" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="650" y="125" text-anchor="middle" font-size="12" font-weight="bold">TRANSMIT</text>
                    <text x="650" y="140" text-anchor="middle" font-size="12" font-weight="bold">BEAMFORMER</text>
                    <text x="650" y="155" text-anchor="middle" font-size="10">Pulse Control</text>

                    <!-- Receive beamformer -->
                    <rect x="720" y="100" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="770" y="125" text-anchor="middle" font-size="12" font-weight="bold">RECEIVE</text>
                    <text x="770" y="140" text-anchor="middle" font-size="12" font-weight="bold">BEAMFORMER</text>
                    <text x="770" y="155" text-anchor="middle" font-size="10">Echo Processing</text>

                    <!-- Delay lines -->
                    <rect x="620" y="180" width="160" height="30" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="700" y="200" text-anchor="middle" font-size="11" font-weight="bold">DELAY LINES</text>
                    <text x="700" y="215" text-anchor="middle" font-size="9">Focusing Control</text>
                </g>

                <!-- Signal processing -->
                <g>
                    <text x="1000" y="80" text-anchor="middle" font-size="16" font-weight="bold">SIGNAL PROCESSING</text>

                    <!-- TGC -->
                    <rect x="900" y="100" width="80" height="50" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="940" y="120" text-anchor="middle" font-size="11" font-weight="bold">TGC</text>
                    <text x="940" y="135" text-anchor="middle" font-size="9">Time Gain</text>
                    <text x="940" y="148" text-anchor="middle" font-size="9">Control</text>

                    <!-- Envelope detection -->
                    <rect x="1000" y="100" width="80" height="50" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="1040" y="115" text-anchor="middle" font-size="10" font-weight="bold">ENVELOPE</text>
                    <text x="1040" y="130" text-anchor="middle" font-size="10" font-weight="bold">DETECTION</text>
                    <text x="1040" y="145" text-anchor="middle" font-size="9">Demodulation</text>

                    <!-- Log compression -->
                    <rect x="1100" y="100" width="80" height="50" fill="#fff3cd" stroke="#ffc107" stroke-width="3"/>
                    <text x="1140" y="115" text-anchor="middle" font-size="10" font-weight="bold">LOG</text>
                    <text x="1140" y="130" text-anchor="middle" font-size="10" font-weight="bold">COMPRESSION</text>
                    <text x="1140" y="145" text-anchor="middle" font-size="9">Dynamic Range</text>
                </g>

                <!-- Patient interaction -->
                <g>
                    <text x="300" y="320" text-anchor="middle" font-size="16" font-weight="bold">ACOUSTIC INTERACTION</text>

                    <!-- Patient -->
                    <ellipse cx="300" cy="380" rx="100" ry="80" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="300" y="385" text-anchor="middle" font-size="14" font-weight="bold">PATIENT</text>
                    <text x="300" y="405" text-anchor="middle" font-size="11">Tissue Interfaces</text>

                    <!-- Incident beam -->
                    <path d="M 300 280 L 300 300" stroke="#9c27b0" stroke-width="6" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 290 285 L 290 305" stroke="#9c27b0" stroke-width="4" fill="none"/>
                    <path d="M 310 285 L 310 305" stroke="#9c27b0" stroke-width="4" fill="none"/>
                    <text x="320" y="290" text-anchor="middle" font-size="10" fill="#9c27b0">Incident</text>

                    <!-- Reflected echoes -->
                    <path d="M 300 460 L 300 480" stroke="#4caf50" stroke-width="4" fill="none" marker-end="url(#arrow)"/>
                    <path d="M 290 465 L 290 485" stroke="#4caf50" stroke-width="3" fill="none"/>
                    <path d="M 310 465 L 310 485" stroke="#4caf50" stroke-width="3" fill="none"/>
                    <text x="320" y="475" text-anchor="middle" font-size="10" fill="#4caf50">Echoes</text>

                    <!-- Scattering -->
                    <path d="M 280 360 L 240 340" stroke="#ff9800" stroke-width="2" fill="none"/>
                    <path d="M 320 360 L 360 340" stroke="#ff9800" stroke-width="2" fill="none"/>
                    <path d="M 280 400 L 240 420" stroke="#ff9800" stroke-width="2" fill="none"/>
                    <path d="M 320 400 L 360 420" stroke="#ff9800" stroke-width="2" fill="none"/>
                    <text x="220" y="330" text-anchor="middle" font-size="9" fill="#ff9800">Scatter</text>
                    <text x="380" y="330" text-anchor="middle" font-size="9" fill="#ff9800">Scatter</text>
                </g>

                <!-- Doppler processing -->
                <g>
                    <text x="700" y="320" text-anchor="middle" font-size="16" font-weight="bold">DOPPLER PROCESSING</text>

                    <!-- Autocorrelation -->
                    <rect x="600" y="340" width="120" height="60" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="660" y="365" text-anchor="middle" font-size="12" font-weight="bold">AUTOCORRELATION</text>
                    <text x="660" y="380" text-anchor="middle" font-size="10">Frequency Analysis</text>
                    <text x="660" y="395" text-anchor="middle" font-size="10">Phase Detection</text>

                    <!-- Wall filter -->
                    <rect x="740" y="340" width="120" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="800" y="365" text-anchor="middle" font-size="12" font-weight="bold">WALL FILTER</text>
                    <text x="800" y="380" text-anchor="middle" font-size="10">Clutter Rejection</text>
                    <text x="800" y="395" text-anchor="middle" font-size="10">Motion Artifact</text>

                    <!-- Color mapping -->
                    <rect x="620" y="420" width="160" height="40" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="700" y="440" text-anchor="middle" font-size="12" font-weight="bold">COLOR MAPPING</text>
                    <text x="700" y="455" text-anchor="middle" font-size="10">Flow Visualization</text>
                </g>

                <!-- Display system -->
                <g>
                    <text x="1000" y="320" text-anchor="middle" font-size="16" font-weight="bold">DISPLAY SYSTEM</text>

                    <!-- Scan converter -->
                    <rect x="900" y="340" width="100" height="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="950" y="365" text-anchor="middle" font-size="12" font-weight="bold">SCAN</text>
                    <text x="950" y="380" text-anchor="middle" font-size="12" font-weight="bold">CONVERTER</text>
                    <text x="950" y="395" text-anchor="middle" font-size="10">Image Formation</text>

                    <!-- Display monitor -->
                    <rect x="1020" y="340" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="1070" y="365" text-anchor="middle" font-size="12" font-weight="bold">DISPLAY</text>
                    <text x="1070" y="380" text-anchor="middle" font-size="12" font-weight="bold">MONITOR</text>
                    <text x="1070" y="395" text-anchor="middle" font-size="10">Real-time Image</text>

                    <!-- Image processing -->
                    <rect x="920" y="420" width="160" height="40" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="1000" y="440" text-anchor="middle" font-size="12" font-weight="bold">IMAGE PROCESSING</text>
                    <text x="1000" y="455" text-anchor="middle" font-size="10">Enhancement & Filtering</text>
                </g>

                <!-- Acoustic properties -->
                <g>
                    <text x="300" y="540" text-anchor="middle" font-size="16" font-weight="bold">ACOUSTIC PROPERTIES</text>

                    <!-- Frequency -->
                    <rect x="150" y="560" width="100" height="60" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="200" y="585" text-anchor="middle" font-size="12" font-weight="bold">FREQUENCY</text>
                    <text x="200" y="600" text-anchor="middle" font-size="10">2-15 MHz</text>
                    <text x="200" y="615" text-anchor="middle" font-size="10">Resolution</text>

                    <!-- Wavelength -->
                    <rect x="270" y="560" width="100" height="60" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="320" y="585" text-anchor="middle" font-size="12" font-weight="bold">WAVELENGTH</text>
                    <text x="320" y="600" text-anchor="middle" font-size="10">λ = c/f</text>
                    <text x="320" y="615" text-anchor="middle" font-size="10">0.1-0.8 mm</text>

                    <!-- Attenuation -->
                    <rect x="390" y="560" width="100" height="60" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="440" y="585" text-anchor="middle" font-size="12" font-weight="bold">ATTENUATION</text>
                    <text x="440" y="600" text-anchor="middle" font-size="10">0.5 dB/cm/MHz</text>
                    <text x="440" y="615" text-anchor="middle" font-size="10">Penetration</text>
                </g>

                <!-- Imaging modes -->
                <g>
                    <text x="800" y="540" text-anchor="middle" font-size="16" font-weight="bold">IMAGING MODES</text>

                    <!-- B-mode -->
                    <rect x="700" y="560" width="80" height="50" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                    <text x="740" y="580" text-anchor="middle" font-size="11" font-weight="bold">B-MODE</text>
                    <text x="740" y="595" text-anchor="middle" font-size="9">Brightness</text>
                    <text x="740" y="608" text-anchor="middle" font-size="9">2D Image</text>

                    <!-- M-mode -->
                    <rect x="800" y="560" width="80" height="50" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="840" y="580" text-anchor="middle" font-size="11" font-weight="bold">M-MODE</text>
                    <text x="840" y="595" text-anchor="middle" font-size="9">Motion</text>
                    <text x="840" y="608" text-anchor="middle" font-size="9">Time Display</text>

                    <!-- Doppler -->
                    <rect x="900" y="560" width="80" height="50" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="940" y="580" text-anchor="middle" font-size="11" font-weight="bold">DOPPLER</text>
                    <text x="940" y="595" text-anchor="middle" font-size="9">Flow</text>
                    <text x="940" y="608" text-anchor="middle" font-size="9">Velocity</text>
                </g>

                <!-- Technical specifications -->
                <g>
                    <text x="200" y="680" font-size="14" font-weight="bold">TECHNICAL SPECIFICATIONS</text>
                    <text x="200" y="700" font-size="12">Frequency Range: 1-15 MHz</text>
                    <text x="200" y="715" font-size="12">Penetration: 1-30 cm</text>
                    <text x="200" y="730" font-size="12">Axial Resolution: 0.1-1 mm</text>
                    <text x="200" y="745" font-size="12">Lateral Resolution: 0.5-3 mm</text>
                    <text x="200" y="760" font-size="12">Frame Rate: 15-100 fps</text>

                    <text x="600" y="680" font-size="14" font-weight="bold">SAFETY PARAMETERS</text>
                    <text x="600" y="700" font-size="12">No Ionizing Radiation</text>
                    <text x="600" y="715" font-size="12">Thermal Index (TI) <1.0</text>
                    <text x="600" y="730" font-size="12">Mechanical Index (MI) <1.9</text>
                    <text x="600" y="745" font-size="12">ALARA Principle</text>
                    <text x="600" y="760" font-size="12">Real-time Monitoring</text>

                    <text x="1000" y="680" font-size="14" font-weight="bold">CLINICAL APPLICATIONS</text>
                    <text x="1000" y="700" font-size="12">Obstetric/Gynecologic</text>
                    <text x="1000" y="715" font-size="12">Cardiac Imaging</text>
                    <text x="1000" y="730" font-size="12">Abdominal Studies</text>
                    <text x="1000" y="745" font-size="12">Vascular Assessment</text>
                    <text x="1000" y="760" font-size="12">Musculoskeletal</text>
                </g>

                <!-- Connection lines -->
                <path d="M 400 130 L 600 130" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <path d="M 820 130 L 900 130" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <path d="M 860 370 L 900 370" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <text x="500" y="120" text-anchor="middle" font-size="10" fill="#333">Transmit/Receive</text>
                <text x="860" y="120" text-anchor="middle" font-size="10" fill="#333">Signal Processing</text>
                <text x="880" y="360" text-anchor="middle" font-size="10" fill="#333">Display</text>

                <!-- Arrow marker -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🔊 Ultrasound Physics and Acoustic Principles</div>

        <div class="physics-section">
            <h2>Acoustic Wave Propagation and Tissue Interaction</h2>

            <div class="formula-box">
                <h4>🌊 Fundamental Ultrasound Physics Equations</h4>
                <p><strong>Wave Equation: c = fλ (velocity = frequency × wavelength)</strong></p>
                <p><strong>Acoustic Impedance: Z = ρc (density × velocity)</strong></p>
                <p><strong>Reflection Coefficient: R = (Z₂-Z₁)²/(Z₂+Z₁)²</strong></p>
                <p><strong>Attenuation: I = I₀e^(-αx) (exponential decay)</strong></p>
            </div>

            <div class="dual-column">
                <div>
                    <h3>🌊 Acoustic Wave Properties</h3>
                    <ul>
                        <li><strong>Frequency Characteristics:</strong></li>
                        <ul>
                            <li>Diagnostic range: 1-15 MHz</li>
                            <li>Higher frequency = better resolution</li>
                            <li>Lower frequency = greater penetration</li>
                            <li>Frequency-dependent attenuation</li>
                        </ul>
                        <li><strong>Wave Propagation:</strong></li>
                        <ul>
                            <li>Longitudinal compression waves</li>
                            <li>Tissue velocity: ~1540 m/s</li>
                            <li>Wavelength determines resolution</li>
                            <li>Beam focusing and divergence</li>
                        </ul>
                        <li><strong>Acoustic Impedance:</strong></li>
                        <ul>
                            <li>Tissue-specific property</li>
                            <li>Determines reflection/transmission</li>
                            <li>Interface contrast mechanism</li>
                            <li>Matching layer optimization</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>🎯 Tissue Interactions</h3>
                    <ul>
                        <li><strong>Reflection Mechanisms:</strong></li>
                        <ul>
                            <li>Specular reflection (smooth interfaces)</li>
                            <li>Rayleigh scattering (small structures)</li>
                            <li>Backscatter from tissue texture</li>
                            <li>Angle-dependent reflection</li>
                        </ul>
                        <li><strong>Attenuation Effects:</strong></li>
                        <ul>
                            <li>Absorption (heat generation)</li>
                            <li>Scattering (energy redirection)</li>
                            <li>Frequency-dependent losses</li>
                            <li>Depth-dependent signal loss</li>
                        </ul>
                        <li><strong>Acoustic Enhancement:</strong></li>
                        <ul>
                            <li>Fluid-filled structures</li>
                            <li>Posterior acoustic enhancement</li>
                            <li>Acoustic shadowing</li>
                            <li>Reverberation artifacts</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">🔧 Transducer Technology and Beam Forming</div>

        <div class="transducer-section">
            <h2>Advanced Transducer Design and Beam Control</h2>
            <div class="dual-column">
                <div>
                    <h3>⚡ Piezoelectric Transducers</h3>
                    <ul>
                        <li><strong>Piezoelectric Materials:</strong></li>
                        <ul>
                            <li>Lead zirconate titanate (PZT)</li>
                            <li>Polyvinylidene fluoride (PVDF)</li>
                            <li>Single crystal materials</li>
                            <li>Composite transducers</li>
                        </ul>
                        <li><strong>Transducer Construction:</strong></li>
                        <ul>
                            <li>Active element design</li>
                            <li>Backing material damping</li>
                            <li>Matching layer optimization</li>
                            <li>Acoustic lens focusing</li>
                        </ul>
                        <li><strong>Array Configurations:</strong></li>
                        <ul>
                            <li>Linear arrays (rectangular images)</li>
                            <li>Curved arrays (sector images)</li>
                            <li>Phased arrays (cardiac imaging)</li>
                            <li>2D matrix arrays (3D imaging)</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>📡 Beam Forming Technology</h3>
                    <ul>
                        <li><strong>Transmit Beam Forming:</strong></li>
                        <ul>
                            <li>Electronic focusing</li>
                            <li>Delay line control</li>
                            <li>Aperture optimization</li>
                            <li>Multi-zone transmission</li>
                        </ul>
                        <li><strong>Receive Beam Forming:</strong></li>
                        <ul>
                            <li>Dynamic focusing</li>
                            <li>Apodization weighting</li>
                            <li>Parallel processing</li>
                            <li>Coherent summation</li>
                        </ul>
                        <li><strong>Advanced Techniques:</strong></li>
                        <ul>
                            <li>Compound imaging</li>
                            <li>Harmonic imaging</li>
                            <li>Coded excitation</li>
                            <li>Adaptive beam forming</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">🌈 Doppler Ultrasound and Flow Assessment</div>

        <div class="doppler-section">
            <h2>Doppler Effect and Hemodynamic Evaluation</h2>
            <div class="dual-column">
                <div>
                    <h3>🌊 Doppler Principles</h3>
                    <ul>
                        <li><strong>Doppler Effect:</strong></li>
                        <ul>
                            <li>Frequency shift detection</li>
                            <li>Moving reflector analysis</li>
                            <li>Velocity calculation</li>
                            <li>Angle dependency</li>
                        </ul>
                        <li><strong>Continuous Wave Doppler:</strong></li>
                        <ul>
                            <li>Dedicated transmit/receive elements</li>
                            <li>High velocity detection</li>
                            <li>No range resolution</li>
                            <li>Spectral analysis</li>
                        </ul>
                        <li><strong>Pulsed Wave Doppler:</strong></li>
                        <ul>
                            <li>Range-gated sampling</li>
                            <li>Depth-specific velocity</li>
                            <li>Nyquist limit constraints</li>
                            <li>Aliasing considerations</li>
                        </ul>
                    </ul>
                </div>
                <div>
                    <h3>🎨 Color Doppler Imaging</h3>
                    <ul>
                        <li><strong>Color Flow Mapping:</strong></li>
                        <ul>
                            <li>2D velocity overlay</li>
                            <li>Autocorrelation processing</li>
                            <li>Color scale assignment</li>
                            <li>Flow direction indication</li>
                        </ul>
                        <li><strong>Power Doppler:</strong></li>
                        <ul>
                            <li>Amplitude-based detection</li>
                            <li>Angle-independent imaging</li>
                            <li>Sensitive flow detection</li>
                            <li>No velocity information</li>
                        </ul>
                        <li><strong>Advanced Doppler:</strong></li>
                        <ul>
                            <li>Directional power Doppler</li>
                            <li>B-flow imaging</li>
                            <li>Vector flow imaging</li>
                            <li>Microvascular imaging</li>
                        </ul>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section-title">📋 Ultrasound Technology Specifications</div>

        <table>
            <thead>
                <tr>
                    <th>System Component</th>
                    <th>Specification Range</th>
                    <th>Performance Parameter</th>
                    <th>Clinical Impact</th>
                    <th>Advanced Features</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Transducer Frequency</strong></td>
                    <td>1-15 MHz</td>
                    <td>Resolution vs penetration</td>
                    <td>Image quality optimization</td>
                    <td>Broadband transducers</td>
                </tr>
                <tr>
                    <td><strong>Axial Resolution</strong></td>
                    <td>0.1-1.0 mm</td>
                    <td>Depth discrimination</td>
                    <td>Detail visualization</td>
                    <td>Harmonic imaging</td>
                </tr>
                <tr>
                    <td><strong>Lateral Resolution</strong></td>
                    <td>0.5-3.0 mm</td>
                    <td>Beam width dependent</td>
                    <td>Structure separation</td>
                    <td>Compound imaging</td>
                </tr>
                <tr>
                    <td><strong>Penetration Depth</strong></td>
                    <td>1-30 cm</td>
                    <td>Frequency dependent</td>
                    <td>Anatomical access</td>
                    <td>Coded excitation</td>
                </tr>
                <tr>
                    <td><strong>Frame Rate</strong></td>
                    <td>15-100 fps</td>
                    <td>Temporal resolution</td>
                    <td>Motion assessment</td>
                    <td>Parallel processing</td>
                </tr>
                <tr>
                    <td><strong>Dynamic Range</strong></td>
                    <td>60-120 dB</td>
                    <td>Contrast sensitivity</td>
                    <td>Tissue differentiation</td>
                    <td>Adaptive processing</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">🎯 Clinical Applications and Specializations</div>

        <div class="ultrasound-grid">
            <div class="ultrasound-card">
                <h3>👶 Obstetric and Gynecologic Ultrasound</h3>
                <ul>
                    <li><strong>Obstetric Applications:</strong></li>
                    <ul>
                        <li>Fetal biometry and growth</li>
                        <li>Anatomical survey</li>
                        <li>Doppler flow assessment</li>
                        <li>3D/4D fetal imaging</li>
                    </ul>
                    <li><strong>Gynecologic Applications:</strong></li>
                    <ul>
                        <li>Pelvic organ evaluation</li>
                        <li>Ovarian and uterine pathology</li>
                        <li>Transvaginal imaging</li>
                        <li>Fertility assessment</li>
                    </ul>
                    <li><strong>Advanced Techniques:</strong></li>
                    <ul>
                        <li>Contrast-enhanced ultrasound</li>
                        <li>Elastography</li>
                        <li>3D volume rendering</li>
                        <li>Automated measurements</li>
                    </ul>
                </ul>
            </div>

            <div class="ultrasound-card">
                <h3>💓 Cardiac Ultrasound (Echocardiography)</h3>
                <ul>
                    <li><strong>Structural Assessment:</strong></li>
                    <ul>
                        <li>Chamber dimensions</li>
                        <li>Wall motion analysis</li>
                        <li>Valve morphology</li>
                        <li>Congenital heart disease</li>
                    </ul>
                    <li><strong>Functional Evaluation:</strong></li>
                    <ul>
                        <li>Ejection fraction</li>
                        <li>Diastolic function</li>
                        <li>Hemodynamic assessment</li>
                        <li>Stress echocardiography</li>
                    </ul>
                    <li><strong>Advanced Applications:</strong></li>
                    <ul>
                        <li>3D echocardiography</li>
                        <li>Strain imaging</li>
                        <li>Contrast echocardiography</li>
                        <li>Transesophageal imaging</li>
                    </ul>
                </ul>
            </div>

            <div class="ultrasound-card">
                <h3>🩸 Vascular Ultrasound</h3>
                <ul>
                    <li><strong>Arterial Assessment:</strong></li>
                    <ul>
                        <li>Carotid artery evaluation</li>
                        <li>Peripheral arterial disease</li>
                        <li>Stenosis quantification</li>
                        <li>Plaque characterization</li>
                    </ul>
                    <li><strong>Venous Evaluation:</strong></li>
                    <ul>
                        <li>Deep vein thrombosis</li>
                        <li>Venous insufficiency</li>
                        <li>Compression studies</li>
                        <li>Reflux assessment</li>
                    </ul>
                    <li><strong>Technical Considerations:</strong></li>
                    <ul>
                        <li>Doppler angle optimization</li>
                        <li>Spectral analysis</li>
                        <li>Color flow imaging</li>
                        <li>Power Doppler sensitivity</li>
                    </ul>
                </ul>
            </div>
        </div>

        <div class="section-title">📚 Summary: Ultrasound Technology Excellence</div>

        <div class="overview-section">
            <h2>Advancing Medical Imaging Through Acoustic Innovation</h2>
            <p>Ultrasound imaging represents the most versatile and accessible diagnostic imaging modality, combining sophisticated acoustic physics, advanced transducer technology, and real-time signal processing to provide comprehensive diagnostic information across numerous medical specialties. The continuous evolution of ultrasound technology, from basic 2D imaging to advanced 3D/4D visualization and functional assessment, demonstrates the field's commitment to improving patient care while maintaining excellent safety profiles and cost-effectiveness. Understanding ultrasound principles, from fundamental acoustics to clinical applications, is essential for healthcare professionals and continues to drive innovation in medical imaging technology.</p>

            <div class="dual-column">
                <div>
                    <h3>🔑 Technology Excellence:</h3>
                    <ul>
                        <li><strong>Acoustic Physics:</strong> Sound wave propagation and tissue interaction</li>
                        <li><strong>Transducer Innovation:</strong> Advanced piezoelectric arrays and beam forming</li>
                        <li><strong>Signal Processing:</strong> Real-time image formation and enhancement</li>
                        <li><strong>Doppler Capabilities:</strong> Flow assessment and hemodynamic evaluation</li>
                        <li><strong>Safety Profile:</strong> No ionizing radiation with excellent bioeffects profile</li>
                    </ul>
                </div>
                <div>
                    <h3>🎯 Clinical Impact:</h3>
                    <ul>
                        <li>Real-time diagnostic imaging</li>
                        <li>Portable and accessible technology</li>
                        <li>Comprehensive obstetric care</li>
                        <li>Cardiac function assessment</li>
                        <li>Vascular disease evaluation</li>
                    </ul>
                </div>
            </div>

            <p><strong>The future of ultrasound technology</strong> continues to advance with artificial intelligence integration, automated measurements, advanced elastography, and novel contrast agents, promising even better image quality, enhanced diagnostic capabilities, and expanded clinical applications for the next generation of acoustic imaging.</p>
        </div>
    </div>
</body>
</html>
