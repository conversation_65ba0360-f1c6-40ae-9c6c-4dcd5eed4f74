<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generations of Computed Tomography</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .generation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .generation-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
            position: relative;
        }
        .generation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .generation-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.4em;
        }
        .generation-number {
            position: absolute;
            top: -10px;
            right: 20px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .timeline-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
            .generation-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Generations of Computed Tomography</h1>

        <div class="timeline-section">
            <h2>Evolution of CT Technology (1972-Present)</h2>
            <p>Computed Tomography has evolved through distinct generations, each representing significant technological advances in scanner design, data acquisition methods, and image reconstruction capabilities. From the original translate-rotate systems to modern multi-detector helical scanners, each generation has improved speed, image quality, and clinical applications.</p>
        </div>

        <div class="section-title">📊 CT Generations Overview</div>

        <div class="generation-grid">
            <div class="generation-card">
                <div class="generation-number">1st</div>
                <h3>First Generation (1972-1976)</h3>
                <p><strong>Configuration:</strong> Single detector, pencil beam</p>
                <p><strong>Motion:</strong> Translate-rotate</p>
                <p><strong>Scan Time:</strong> 4-5 minutes per slice</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Single X-ray tube and detector</li>
                    <li>Parallel beam geometry</li>
                    <li>180 translations × 180 rotations</li>
                    <li>Head-only scanning</li>
                    <li>Matrix: 80×80 pixels</li>
                </ul>
                <p><strong>Limitations:</strong> Very slow, head only, motion artifacts</p>
            </div>

            <div class="generation-card">
                <div class="generation-number">2nd</div>
                <h3>Second Generation (1976-1981)</h3>
                <p><strong>Configuration:</strong> Multiple detectors, fan beam</p>
                <p><strong>Motion:</strong> Translate-rotate</p>
                <p><strong>Scan Time:</strong> 20-60 seconds per slice</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Linear array of 3-52 detectors</li>
                    <li>Narrow fan beam (10-30°)</li>
                    <li>Fewer translations needed</li>
                    <li>Body scanning capability</li>
                    <li>Matrix: 160×160 pixels</li>
                </ul>
                <p><strong>Improvements:</strong> Faster scanning, whole body imaging</p>
            </div>

            <div class="generation-card">
                <div class="generation-number">3rd</div>
                <h3>Third Generation (1981-1989)</h3>
                <p><strong>Configuration:</strong> Rotate-rotate</p>
                <p><strong>Motion:</strong> Continuous rotation</p>
                <p><strong>Scan Time:</strong> 1-10 seconds per slice</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Wide fan beam (40-55°)</li>
                    <li>300-700 detectors</li>
                    <li>Tube and detectors rotate together</li>
                    <li>No translation motion</li>
                    <li>Matrix: 256×256 or 512×512</li>
                </ul>
                <p><strong>Advantages:</strong> Much faster, better image quality</p>
            </div>

            <div class="generation-card">
                <div class="generation-number">4th</div>
                <h3>Fourth Generation (1981-1990s)</h3>
                <p><strong>Configuration:</strong> Rotate-stationary</p>
                <p><strong>Motion:</strong> Tube rotates, detectors fixed</p>
                <p><strong>Scan Time:</strong> 1-10 seconds per slice</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Stationary detector ring (600-4800 detectors)</li>
                    <li>Only X-ray tube rotates</li>
                    <li>360° detector coverage</li>
                    <li>Reduced mechanical complexity</li>
                    <li>Better detector utilization</li>
                </ul>
                <p><strong>Benefits:</strong> Improved stability, calibration advantages</p>
            </div>

            <div class="generation-card">
                <div class="generation-number">5th</div>
                <h3>Fifth Generation (1990s-2000s)</h3>
                <p><strong>Configuration:</strong> Helical/Spiral CT</p>
                <p><strong>Motion:</strong> Continuous rotation + table movement</p>
                <p><strong>Scan Time:</strong> Continuous volume acquisition</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Slip-ring technology</li>
                    <li>Continuous gantry rotation</li>
                    <li>Simultaneous table translation</li>
                    <li>Volume data acquisition</li>
                    <li>Interpolation algorithms</li>
                </ul>
                <p><strong>Revolution:</strong> 3D imaging, faster scans, better MPR</p>
            </div>

            <div class="generation-card">
                <div class="generation-number">6th</div>
                <h3>Sixth Generation (2000s-Present)</h3>
                <p><strong>Configuration:</strong> Multi-detector CT (MDCT)</p>
                <p><strong>Motion:</strong> Helical with multiple detector rows</p>
                <p><strong>Scan Time:</strong> Sub-second per rotation</p>
                <p><strong>Key Features:</strong></p>
                <ul>
                    <li>Multiple detector rows (4-320+)</li>
                    <li>Simultaneous slice acquisition</li>
                    <li>Isotropic voxels</li>
                    <li>Advanced reconstruction algorithms</li>
                    <li>Cardiac and dynamic imaging</li>
                </ul>
                <p><strong>Current State:</strong> Ultra-fast, high-resolution, 4D imaging</p>
            </div>
        </div>

        <div class="section-title">📊 Technical Comparison Table</div>

        <table>
            <thead>
                <tr>
                    <th>Generation</th>
                    <th>Beam Geometry</th>
                    <th>Detectors</th>
                    <th>Motion Type</th>
                    <th>Scan Time</th>
                    <th>Matrix Size</th>
                    <th>Key Innovation</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>1st (1972)</strong></td>
                    <td>Pencil beam</td>
                    <td>1 detector</td>
                    <td>Translate-rotate</td>
                    <td>4-5 minutes</td>
                    <td>80×80</td>
                    <td>First CT scanner</td>
                </tr>
                <tr>
                    <td><strong>2nd (1976)</strong></td>
                    <td>Narrow fan (10-30°)</td>
                    <td>3-52 detectors</td>
                    <td>Translate-rotate</td>
                    <td>20-60 seconds</td>
                    <td>160×160</td>
                    <td>Multiple detectors</td>
                </tr>
                <tr>
                    <td><strong>3rd (1981)</strong></td>
                    <td>Wide fan (40-55°)</td>
                    <td>300-700 detectors</td>
                    <td>Rotate-rotate</td>
                    <td>1-10 seconds</td>
                    <td>256×256, 512×512</td>
                    <td>Continuous rotation</td>
                </tr>
                <tr>
                    <td><strong>4th (1981)</strong></td>
                    <td>Fan beam</td>
                    <td>600-4800 detectors</td>
                    <td>Rotate-stationary</td>
                    <td>1-10 seconds</td>
                    <td>512×512</td>
                    <td>Stationary detector ring</td>
                </tr>
                <tr>
                    <td><strong>5th (1989)</strong></td>
                    <td>Fan beam</td>
                    <td>700-900 detectors</td>
                    <td>Helical/spiral</td>
                    <td>Continuous</td>
                    <td>512×512</td>
                    <td>Volume acquisition</td>
                </tr>
                <tr>
                    <td><strong>6th (1998)</strong></td>
                    <td>Cone beam</td>
                    <td>Multiple rows (4-320+)</td>
                    <td>Multi-detector helical</td>
                    <td>0.3-1.0 seconds</td>
                    <td>512×512+</td>
                    <td>Simultaneous slices</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">🚀 Modern CT Advances & Future Directions</div>

        <div class="generation-grid">
            <div class="generation-card">
                <h3>🔬 Current Technology (6th Gen)</h3>
                <p><strong>Multi-Detector CT Features:</strong></p>
                <ul>
                    <li><span class="highlight">Detector Arrays:</span> Up to 320+ rows</li>
                    <li><span class="highlight">Rotation Speed:</span> 0.28-0.35 seconds</li>
                    <li><span class="highlight">Coverage:</span> 16cm per rotation</li>
                    <li><span class="highlight">Resolution:</span> 0.35mm isotropic</li>
                </ul>
                <p><strong>Clinical Applications:</strong></p>
                <ul>
                    <li>Cardiac CT angiography</li>
                    <li>Perfusion studies</li>
                    <li>4D imaging</li>
                    <li>Dual-energy CT</li>
                </ul>
            </div>

            <div class="generation-card">
                <h3>🔮 Emerging Technologies</h3>
                <p><strong>Photon Counting Detectors:</strong></p>
                <ul>
                    <li>Direct X-ray conversion</li>
                    <li>Energy discrimination</li>
                    <li>Improved contrast resolution</li>
                    <li>Reduced radiation dose</li>
                </ul>
                <p><strong>AI Integration:</strong></p>
                <ul>
                    <li>Deep learning reconstruction</li>
                    <li>Noise reduction algorithms</li>
                    <li>Automated diagnosis</li>
                    <li>Dose optimization</li>
                </ul>
            </div>

            <div class="generation-card">
                <h3>🌟 Future Possibilities</h3>
                <p><strong>7th Generation Concepts:</strong></p>
                <ul>
                    <li><span class="highlight">Spectral CT:</span> Multi-energy imaging</li>
                    <li><span class="highlight">Stationary CT:</span> No moving parts</li>
                    <li><span class="highlight">Real-time CT:</span> Live imaging</li>
                    <li><span class="highlight">Molecular CT:</span> Functional imaging</li>
                </ul>
                <p><strong>Research Areas:</strong></p>
                <ul>
                    <li>Carbon nanotube X-ray sources</li>
                    <li>Distributed X-ray arrays</li>
                    <li>Ultra-low dose protocols</li>
                    <li>Portable CT systems</li>
                </ul>
            </div>
        </div>

        <div class="timeline-section">
            <h2>🎯 Impact & Clinical Significance</h2>
            <div class="dual-column">
                <div>
                    <h3>📈 Performance Evolution</h3>
                    <ul>
                        <li><strong>Speed:</strong> 300 seconds → 0.3 seconds (1000× faster)</li>
                        <li><strong>Resolution:</strong> 3mm → 0.35mm (8× better)</li>
                        <li><strong>Coverage:</strong> Single slice → 16cm volume</li>
                        <li><strong>Dose:</strong> Significant reduction with modern protocols</li>
                    </ul>

                    <h3>🏥 Clinical Applications</h3>
                    <ul>
                        <li><strong>1st-2nd Gen:</strong> Basic head/body imaging</li>
                        <li><strong>3rd-4th Gen:</strong> Routine diagnostic imaging</li>
                        <li><strong>5th Gen:</strong> 3D reconstruction, angiography</li>
                        <li><strong>6th Gen:</strong> Cardiac, perfusion, 4D studies</li>
                    </ul>
                </div>
                <div>
                    <h3>🔬 Technological Milestones</h3>
                    <ul>
                        <li><strong>1979:</strong> Nobel Prize (Hounsfield & Cormack)</li>
                        <li><strong>1989:</strong> Slip-ring technology</li>
                        <li><strong>1998:</strong> First 4-slice MDCT</li>
                        <li><strong>2004:</strong> 64-slice scanners</li>
                        <li><strong>2007:</strong> 320-slice volume CT</li>
                        <li><strong>2013:</strong> Dual-source CT</li>
                        <li><strong>2021:</strong> Photon counting CT</li>
                    </ul>

                    <h3>🌍 Global Impact</h3>
                    <ul>
                        <li>Over 100 million CT scans annually worldwide</li>
                        <li>Essential for emergency medicine</li>
                        <li>Cancer screening and staging</li>
                        <li>Surgical planning and guidance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>