<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EEG Electroencephalogram - Brain Activity Monitoring System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .clinical-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 EEG Electroencephalogram - Brain Activity Monitoring System</h1>

        <div class="overview-section">
            <h2>Overview of Electroencephalography</h2>
            <p>The electroencephalogram (EEG) is a sophisticated neurophysiological monitoring technique that records electrical activity generated by neurons in the brain. This non-invasive diagnostic tool captures brain waves through strategically placed scalp electrodes, amplifying these minute electrical signals (typically 10-100 μV) to reveal patterns of neural activity, sleep stages, seizure activity, and various neurological conditions. Modern EEG systems integrate advanced digital signal processing, automated artifact rejection, frequency analysis, and real-time monitoring capabilities, making them essential tools in neurology, epilepsy monitoring, sleep medicine, anesthesia monitoring, and neuroscience research.</p>
        </div>

        <div class="section-title">🔧 EEG System Block Diagram & Architecture</div>

        <div class="diagram-container">
            <svg width="100%" height="800" viewBox="0 0 1200 800">
                <!-- Title -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">EEG ELECTROENCEPHALOGRAM SYSTEM BLOCK DIAGRAM</text>

                <!-- Patient and electrode cap -->
                <g>
                    <text x="150" y="80" text-anchor="middle" font-size="14" font-weight="bold">PATIENT INTERFACE</text>

                    <!-- Head with electrode cap -->
                    <circle cx="150" cy="140" r="50" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="150" y="145" text-anchor="middle" font-size="12" font-weight="bold">BRAIN</text>

                    <!-- EEG electrodes on scalp -->
                    <circle cx="130" cy="110" r="6" fill="#ff9800"/>
                    <text x="130" y="114" text-anchor="middle" font-size="5" fill="white">Fp1</text>
                    <circle cx="170" cy="110" r="6" fill="#ff9800"/>
                    <text x="170" y="114" text-anchor="middle" font-size="5" fill="white">Fp2</text>
                    <circle cx="120" cy="140" r="6" fill="#ff9800"/>
                    <text x="120" y="144" text-anchor="middle" font-size="5" fill="white">F7</text>
                    <circle cx="180" cy="140" r="6" fill="#ff9800"/>
                    <text x="180" y="144" text-anchor="middle" font-size="5" fill="white">F8</text>
                    <circle cx="135" cy="140" r="6" fill="#ff9800"/>
                    <text x="135" y="144" text-anchor="middle" font-size="5" fill="white">F3</text>
                    <circle cx="165" cy="140" r="6" fill="#ff9800"/>
                    <text x="165" y="144" text-anchor="middle" font-size="5" fill="white">F4</text>
                    <circle cx="150" cy="125" r="6" fill="#ff9800"/>
                    <text x="150" y="129" text-anchor="middle" font-size="5" fill="white">Fz</text>
                    <circle cx="150" cy="155" r="6" fill="#ff9800"/>
                    <text x="150" y="159" text-anchor="middle" font-size="5" fill="white">Cz</text>
                    <circle cx="120" cy="170" r="6" fill="#ff9800"/>
                    <text x="120" y="174" text-anchor="middle" font-size="5" fill="white">T3</text>
                    <circle cx="180" cy="170" r="6" fill="#ff9800"/>
                    <text x="180" y="174" text-anchor="middle" font-size="5" fill="white">T4</text>

                    <text x="150" y="210" text-anchor="middle" font-size="10">10-20 Electrode System</text>
                    <text x="150" y="225" text-anchor="middle" font-size="10">19-21 channels</text>
                </g>

                <!-- Electrode selector/multiplexer -->
                <g>
                    <rect x="280" y="100" width="120" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="340" y="125" text-anchor="middle" font-size="12" font-weight="bold">ELECTRODE</text>
                    <text x="340" y="140" text-anchor="middle" font-size="12" font-weight="bold">SELECTOR</text>
                    <text x="340" y="155" text-anchor="middle" font-size="10">• 19-21 channels</text>
                    <text x="340" y="170" text-anchor="middle" font-size="10">• Impedance check</text>
                </g>

                <!-- High-gain amplifier -->
                <g>
                    <rect x="450" y="100" width="120" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="510" y="125" text-anchor="middle" font-size="12" font-weight="bold">HIGH-GAIN</text>
                    <text x="510" y="140" text-anchor="middle" font-size="12" font-weight="bold">AMPLIFIER</text>
                    <text x="510" y="155" text-anchor="middle" font-size="10">• Gain: 10,000-100,000x</text>
                    <text x="510" y="170" text-anchor="middle" font-size="10">• Low noise: <2μV</text>
                </g>

                <!-- Filter bank -->
                <g>
                    <rect x="620" y="100" width="120" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="680" y="125" text-anchor="middle" font-size="12" font-weight="bold">FILTER BANK</text>
                    <text x="680" y="140" text-anchor="middle" font-size="10">• Low-pass: 70 Hz</text>
                    <text x="680" y="155" text-anchor="middle" font-size="10">• High-pass: 0.5 Hz</text>
                    <text x="680" y="170" text-anchor="middle" font-size="10">• Notch: 50/60 Hz</text>
                </g>

                <!-- ADC -->
                <g>
                    <rect x="790" y="100" width="120" height="80" fill="#bbdefb" stroke="#2196f3" stroke-width="3"/>
                    <text x="850" y="125" text-anchor="middle" font-size="12" font-weight="bold">ADC CONVERTER</text>
                    <text x="850" y="140" text-anchor="middle" font-size="10">• 24-bit resolution</text>
                    <text x="850" y="155" text-anchor="middle" font-size="10">• 256-1024 Hz sampling</text>
                    <text x="850" y="170" text-anchor="middle" font-size="10">• Multi-channel</text>
                </g>

                <!-- Digital signal processor -->
                <g>
                    <rect x="960" y="100" width="120" height="80" fill="#dcedc8" stroke="#689f38" stroke-width="3"/>
                    <text x="1020" y="125" text-anchor="middle" font-size="12" font-weight="bold">DIGITAL</text>
                    <text x="1020" y="140" text-anchor="middle" font-size="12" font-weight="bold">PROCESSOR</text>
                    <text x="1020" y="155" text-anchor="middle" font-size="10">• FFT analysis</text>
                    <text x="1020" y="170" text-anchor="middle" font-size="10">• Artifact rejection</text>
                </g>

                <!-- Signal flow arrows -->
                <path d="M 200 140 L 280 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 400 140 L 450 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 570 140 L 620 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 740 140 L 790 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 910 140 L 960 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                <!-- Analysis and display systems -->
                <g>
                    <rect x="500" y="250" width="200" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="600" y="280" text-anchor="middle" font-size="14" font-weight="bold">ANALYSIS SYSTEM</text>
                    <text x="600" y="300" text-anchor="middle" font-size="11">• Frequency analysis (FFT)</text>
                    <text x="600" y="315" text-anchor="middle" font-size="11">• Spike detection algorithms</text>
                    <text x="600" y="330" text-anchor="middle" font-size="11">• Sleep stage classification</text>
                    <text x="600" y="345" text-anchor="middle" font-size="11">• Seizure detection</text>
                </g>

                <!-- Output systems -->
                <g>
                    <text x="600" y="400" text-anchor="middle" font-size="16" font-weight="bold">OUTPUT & DISPLAY SYSTEMS</text>

                    <!-- Multi-channel display -->
                    <rect x="100" y="430" width="200" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                    <text x="200" y="455" text-anchor="middle" font-size="12" font-weight="bold">MULTI-CHANNEL DISPLAY</text>
                    <rect x="120" y="470" width="160" height="60" fill="#000" stroke="#333" stroke-width="1"/>
                    <!-- EEG waveforms -->
                    <path d="M 130 480 Q 140 475 150 480 Q 160 485 170 480 Q 180 475 190 480 Q 200 485 210 480 Q 220 475 230 480 Q 240 485 250 480 Q 260 475 270 480"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <path d="M 130 490 Q 140 485 150 490 Q 160 495 170 490 Q 180 485 190 490 Q 200 495 210 490 Q 220 485 230 490 Q 240 495 250 490 Q 260 485 270 490"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <path d="M 130 500 Q 140 495 150 500 Q 160 505 170 500 Q 180 495 190 500 Q 200 505 210 500 Q 220 495 230 500 Q 240 505 250 500 Q 260 495 270 500"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <path d="M 130 510 Q 140 505 150 510 Q 160 515 170 510 Q 180 505 190 510 Q 200 515 210 510 Q 220 505 230 510 Q 240 515 250 510 Q 260 505 270 510"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <path d="M 130 520 Q 140 515 150 520 Q 160 525 170 520 Q 180 515 190 520 Q 200 525 210 520 Q 220 515 230 520 Q 240 525 250 520 Q 260 515 270 520"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <text x="200" y="545" text-anchor="middle" font-size="10">Real-time brain waves</text>

                    <!-- Spectral analysis -->
                    <rect x="350" y="430" width="200" height="120" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="450" y="455" text-anchor="middle" font-size="12" font-weight="bold">SPECTRAL ANALYSIS</text>
                    <rect x="370" y="470" width="160" height="60" fill="#000" stroke="#333" stroke-width="1"/>
                    <!-- Frequency spectrum bars -->
                    <rect x="380" y="520" width="10" height="10" fill="#ff0000"/>
                    <rect x="395" y="515" width="10" height="15" fill="#ff4500"/>
                    <rect x="410" y="510" width="10" height="20" fill="#ffa500"/>
                    <rect x="425" y="505" width="10" height="25" fill="#ffff00"/>
                    <rect x="440" y="500" width="10" height="30" fill="#9acd32"/>
                    <rect x="455" y="510" width="10" height="20" fill="#00ff00"/>
                    <rect x="470" y="515" width="10" height="15" fill="#00ffff"/>
                    <rect x="485" y="520" width="10" height="10" fill="#0000ff"/>
                    <rect x="500" y="525" width="10" height="5" fill="#8a2be2"/>
                    <text x="450" y="545" text-anchor="middle" font-size="10">Frequency domain</text>

                    <!-- Computer workstation -->
                    <rect x="600" y="430" width="200" height="120" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="700" y="455" text-anchor="middle" font-size="12" font-weight="bold">COMPUTER WORKSTATION</text>
                    <text x="700" y="475" text-anchor="middle" font-size="10">• Digital storage</text>
                    <text x="700" y="490" text-anchor="middle" font-size="10">• Automated analysis</text>
                    <text x="700" y="505" text-anchor="middle" font-size="10">• Report generation</text>
                    <text x="700" y="520" text-anchor="middle" font-size="10">• Network connectivity</text>
                    <text x="700" y="535" text-anchor="middle" font-size="10">• Database integration</text>

                    <!-- Video monitoring -->
                    <rect x="850" y="430" width="200" height="120" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="950" y="455" text-anchor="middle" font-size="12" font-weight="bold">VIDEO MONITORING</text>
                    <rect x="870" y="470" width="160" height="40" fill="#333" stroke="#666" stroke-width="1"/>
                    <circle cx="950" cy="490" r="15" fill="#666"/>
                    <text x="950" y="495" text-anchor="middle" font-size="8" fill="white">📹</text>
                    <text x="950" y="525" text-anchor="middle" font-size="10">• Synchronized recording</text>
                    <text x="950" y="540" text-anchor="middle" font-size="10">• Behavioral correlation</text>
                </g>

                <!-- Connection lines -->
                <path d="M 1020 180 L 1020 220 L 600 220 L 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 200 350 L 200 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 450 350 L 450 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 700 350 L 700 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 950 350 L 950 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                <!-- Power and safety systems -->
                <g>
                    <text x="200" y="600" font-size="14" font-weight="bold">POWER SUPPLY</text>
                    <rect x="100" y="620" width="200" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="200" y="645" text-anchor="middle" font-size="11">• Ultra-low noise design</text>
                    <text x="200" y="660" text-anchor="middle" font-size="11">• Battery backup system</text>
                    <text x="200" y="675" text-anchor="middle" font-size="11">• Isolated patient circuits</text>
                    <text x="200" y="690" text-anchor="middle" font-size="11">• EMI/RFI shielding</text>
                </g>

                <g>
                    <text x="600" y="600" font-size="14" font-weight="bold">CALIBRATION & TESTING</text>
                    <rect x="500" y="620" width="200" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="600" y="645" text-anchor="middle" font-size="11">• Built-in test signals</text>
                    <text x="600" y="660" text-anchor="middle" font-size="11">• Impedance measurement</text>
                    <text x="600" y="675" text-anchor="middle" font-size="11">• System verification</text>
                    <text x="600" y="690" text-anchor="middle" font-size="11">• Automatic calibration</text>
                </g>

                <g>
                    <text x="1000" y="600" font-size="14" font-weight="bold">SAFETY SYSTEMS</text>
                    <rect x="900" y="620" width="200" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="1000" y="645" text-anchor="middle" font-size="11">• Patient isolation</text>
                    <text x="1000" y="660" text-anchor="middle" font-size="11">• Overload protection</text>
                    <text x="1000" y="675" text-anchor="middle" font-size="11">• Ground fault detection</text>
                    <text x="1000" y="690" text-anchor="middle" font-size="11">• Emergency shutdown</text>
                </g>

                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🧠 Brain Wave Patterns & Electrode Placement</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>🔴 Alpha Waves (8-13 Hz)</h3>
                <p><strong>Frequency:</strong> 8-13 Hz</p>
                <p><strong>Amplitude:</strong> 20-200 μV</p>
                <p><strong>Location:</strong> Posterior regions (occipital, parietal)</p>
                <p><strong>State:</strong> Relaxed wakefulness, eyes closed</p>
                <p><strong>Clinical Significance:</strong> Normal adult rhythm</p>
                <p><strong>Pathology:</strong> Asymmetry may indicate lesions</p>
            </div>

            <div class="component-card">
                <h3>🔵 Beta Waves (13-30 Hz)</h3>
                <p><strong>Frequency:</strong> 13-30 Hz</p>
                <p><strong>Amplitude:</strong> 5-30 μV</p>
                <p><strong>Location:</strong> Frontal and central regions</p>
                <p><strong>State:</strong> Alert, focused mental activity</p>
                <p><strong>Clinical Significance:</strong> Normal waking rhythm</p>
                <p><strong>Pathology:</strong> Excessive beta in anxiety, medications</p>
            </div>

            <div class="component-card">
                <h3>🟡 Theta Waves (4-8 Hz)</h3>
                <p><strong>Frequency:</strong> 4-8 Hz</p>
                <p><strong>Amplitude:</strong> Variable</p>
                <p><strong>Location:</strong> Temporal and frontal regions</p>
                <p><strong>State:</strong> Drowsiness, light sleep, meditation</p>
                <p><strong>Clinical Significance:</strong> Normal in children</p>
                <p><strong>Pathology:</strong> Excessive in adults may indicate dysfunction</p>
            </div>

            <div class="component-card">
                <h3>🟠 Delta Waves (0.5-4 Hz)</h3>
                <p><strong>Frequency:</strong> 0.5-4 Hz</p>
                <p><strong>Amplitude:</strong> 100-200 μV</p>
                <p><strong>Location:</strong> Widespread distribution</p>
                <p><strong>State:</strong> Deep sleep (stages 3-4)</p>
                <p><strong>Clinical Significance:</strong> Normal during deep sleep</p>
                <p><strong>Pathology:</strong> Focal delta may indicate lesions</p>
            </div>

            <div class="component-card">
                <h3>⚡ Gamma Waves (30-100 Hz)</h3>
                <p><strong>Frequency:</strong> 30-100 Hz</p>
                <p><strong>Amplitude:</strong> Very low (<10 μV)</p>
                <p><strong>Location:</strong> Various cortical areas</p>
                <p><strong>State:</strong> High-level cognitive processing</p>
                <p><strong>Clinical Significance:</strong> Consciousness, binding</p>
                <p><strong>Research:</strong> Associated with awareness, attention</p>
            </div>

            <div class="component-card">
                <h3>📍 10-20 Electrode System</h3>
                <p><strong>Standard Positions:</strong> 19-21 electrodes</p>
                <p><strong>Nomenclature:</strong> Letter + number system</p>
                <p><strong>Frontal:</strong> Fp1, Fp2, F3, F4, F7, F8, Fz</p>
                <p><strong>Central:</strong> C3, C4, Cz</p>
                <p><strong>Parietal:</strong> P3, P4, Pz</p>
                <p><strong>Occipital:</strong> O1, O2</p>
                <p><strong>Temporal:</strong> T3, T4, T5, T6</p>
                <p><strong>Reference:</strong> A1, A2 (earlobes)</p>
            </div>
        </div>

        <div class="section-title">🏥 Clinical Applications & Diagnostic Uses</div>

        <div class="clinical-section">
            <h2>EEG in Clinical Practice</h2>
            <p>EEG is essential in neurology for diagnosing epilepsy, monitoring brain function during surgery, assessing consciousness levels, sleep disorders evaluation, and detecting various neurological conditions. Modern EEG systems provide continuous monitoring capabilities, automated seizure detection, and sophisticated analysis tools that enable precise diagnosis and treatment monitoring in both inpatient and outpatient settings.</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Clinical Application</th>
                    <th>EEG Findings</th>
                    <th>Key Features</th>
                    <th>Diagnostic Value</th>
                    <th>Monitoring Duration</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Epilepsy Diagnosis</strong></td>
                    <td>Spike-wave complexes, sharp waves</td>
                    <td>Interictal and ictal patterns</td>
                    <td>Seizure focus localization</td>
                    <td>30 min - 72 hours</td>
                </tr>
                <tr>
                    <td><strong>Sleep Disorders</strong></td>
                    <td>Sleep stage patterns, spindles, K-complexes</td>
                    <td>REM/NREM architecture</td>
                    <td>Sleep quality assessment</td>
                    <td>8-12 hours (overnight)</td>
                </tr>
                <tr>
                    <td><strong>Coma Assessment</strong></td>
                    <td>Background activity, reactivity</td>
                    <td>Continuous monitoring</td>
                    <td>Prognosis determination</td>
                    <td>Continuous (days)</td>
                </tr>
                <tr>
                    <td><strong>Brain Death</strong></td>
                    <td>Electrocerebral silence</td>
                    <td>No electrical activity >2μV</td>
                    <td>Legal brain death confirmation</td>
                    <td>30 minutes minimum</td>
                </tr>
                <tr>
                    <td><strong>Intraoperative Monitoring</strong></td>
                    <td>Real-time brain activity</td>
                    <td>Anesthesia depth, seizure detection</td>
                    <td>Surgical safety monitoring</td>
                    <td>Duration of surgery</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">⚙️ Technical Specifications & Maintenance</div>

        <div class="dual-column">
            <div class="clinical-section">
                <h3>System Specifications</h3>
                <ul>
                    <li><span class="highlight">Input Range:</span> ±500 μV full scale</li>
                    <li><span class="highlight">Resolution:</span> 24-bit ADC (0.03 μV/LSB)</li>
                    <li><span class="highlight">Sampling Rate:</span> 256-1024 Hz per channel</li>
                    <li><span class="highlight">Frequency Response:</span> 0.1-70 Hz (±3dB)</li>
                    <li><span class="highlight">CMRR:</span> >110 dB at 50/60 Hz</li>
                    <li><span class="highlight">Input Impedance:</span> >100 MΩ</li>
                    <li><span class="highlight">Noise Level:</span> <2 μV RMS</li>
                    <li><span class="highlight">Channels:</span> 19-256 channels</li>
                </ul>
            </div>

            <div class="clinical-section">
                <h3>Calibration & Maintenance</h3>
                <ul>
                    <li><span class="highlight">Daily:</span> System check, impedance testing</li>
                    <li><span class="highlight">Weekly:</span> Electrode cap cleaning, cable inspection</li>
                    <li><span class="highlight">Monthly:</span> Calibration verification, performance test</li>
                    <li><span class="highlight">Quarterly:</span> Full system validation</li>
                    <li><span class="highlight">Annually:</span> Professional service and certification</li>
                    <li><span class="highlight">Electrode Care:</span> Proper cleaning, gel removal</li>
                    <li><span class="highlight">Storage:</span> Controlled environment, proper handling</li>
                    <li><span class="highlight">Documentation:</span> Maintenance logs, calibration records</li>
                </ul>
            </div>
        </div>

        <div class="section-title">🔧 Troubleshooting & Safety Protocols</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>⚠️ Common Issues & Solutions</h3>
                <p><strong>High Impedance:</strong> Clean electrodes, fresh gel application</p>
                <p><strong>60 Hz Interference:</strong> Check grounding, enable notch filter</p>
                <p><strong>Muscle Artifacts:</strong> Patient relaxation, electrode repositioning</p>
                <p><strong>Eye Movement Artifacts:</strong> Proper electrode placement, artifact rejection</p>
                <p><strong>Baseline Drift:</strong> Check connections, electrode stability</p>
                <p><strong>Poor Signal Quality:</strong> Skin preparation, impedance check</p>
            </div>

            <div class="component-card">
                <h3>🛡️ Safety & Compliance</h3>
                <p><strong>Electrical Safety:</strong> IEC 60601-1 compliance</p>
                <p><strong>Patient Isolation:</strong> Medical grade isolation</p>
                <p><strong>EMC Standards:</strong> IEC 60601-1-2 compliance</p>
                <p><strong>Infection Control:</strong> Disposable electrodes, proper cleaning</p>
                <p><strong>Data Security:</strong> HIPAA compliant storage</p>
                <p><strong>Emergency Procedures:</strong> Quick disconnect protocols</p>
            </div>

            <div class="component-card">
                <h3>📊 Quality Assurance</h3>
                <p><strong>Signal Validation:</strong> Real-time quality metrics</p>
                <p><strong>Artifact Detection:</strong> Automated algorithms</p>
                <p><strong>Data Integrity:</strong> Digital signatures, checksums</p>
                <p><strong>Backup Systems:</strong> Redundant data storage</p>
                <p><strong>Network Security:</strong> Encrypted transmission</p>
                <p><strong>Audit Trails:</strong> Complete recording history</p>
            </div>
        </div>

    </div>
</body>
</html>
