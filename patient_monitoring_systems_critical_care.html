<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Monitoring Systems in Critical Care</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .monitoring-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .monitoring-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .monitoring-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .vital-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        .advanced-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #17a2b8;
        }
        .integration-section {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Patient Monitoring Systems in Critical Care</h1>
        
        <div class="overview-section">
            <h2>Advanced Physiological Monitoring for Critical Care Excellence</h2>
            <p>Patient monitoring systems in critical care represent the technological backbone of modern intensive care medicine, providing continuous, real-time assessment of vital physiological parameters that guide life-saving interventions. These sophisticated systems integrate multiple sensing technologies, advanced signal processing, intelligent alarm management, and comprehensive data analytics to create a complete picture of patient status. From basic vital signs monitoring to complex hemodynamic assessment and neurological monitoring, these systems enable healthcare teams to detect changes in patient condition immediately, optimize therapeutic interventions, and improve clinical outcomes in the most critically ill patients.</p>
        </div>

        <div class="section-title">📈 Comprehensive Patient Monitoring Architecture</div>
        
        <div class="diagram-container">
            <svg width="100%" height="800" viewBox="0 0 1200 800">
                <!-- Patient Monitoring Systems Overview -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">PATIENT MONITORING SYSTEMS ARCHITECTURE</text>
                
                <!-- Central monitoring station -->
                <g>
                    <text x="600" y="80" text-anchor="middle" font-size="16" font-weight="bold">CENTRAL MONITORING STATION</text>
                    
                    <!-- Main console -->
                    <rect x="550" y="100" width="100" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="3"/>
                    <text x="600" y="125" text-anchor="middle" font-size="12" font-weight="bold">Central Console</text>
                    <text x="600" y="140" text-anchor="middle" font-size="10">Multi-patient display</text>
                    <text x="600" y="155" text-anchor="middle" font-size="10">Alarm management</text>
                    <text x="600" y="170" text-anchor="middle" font-size="10">Trend analysis</text>
                    
                    <!-- Server system -->
                    <rect x="520" y="200" width="60" height="40" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="550" y="220" text-anchor="middle" font-size="10" font-weight="bold">Data Server</text>
                    <text x="550" y="235" text-anchor="middle" font-size="9">Storage & Analytics</text>
                    
                    <!-- Network hub -->
                    <rect x="620" y="200" width="60" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                    <text x="650" y="220" text-anchor="middle" font-size="10" font-weight="bold">Network Hub</text>
                    <text x="650" y="235" text-anchor="middle" font-size="9">Communication</text>
                </g>
                
                <!-- Bedside monitors -->
                <g>
                    <text x="200" y="80" text-anchor="middle" font-size="14" font-weight="bold">BEDSIDE MONITORS</text>
                    
                    <!-- Monitor 1 -->
                    <rect x="150" y="100" width="100" height="80" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
                    <text x="200" y="120" text-anchor="middle" font-size="11" font-weight="bold">ICU Monitor 1</text>
                    <rect x="160" y="130" width="80" height="30" fill="#000" stroke="#333" stroke-width="1"/>
                    <text x="200" y="145" text-anchor="middle" font-size="8" fill="#0f0">HR: 85 bpm</text>
                    <text x="200" y="155" text-anchor="middle" font-size="8" fill="#0f0">BP: 120/80</text>
                    <text x="200" y="170" text-anchor="middle" font-size="9">Multi-parameter</text>
                    
                    <!-- Monitor 2 -->
                    <rect x="150" y="200" width="100" height="80" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
                    <text x="200" y="220" text-anchor="middle" font-size="11" font-weight="bold">ICU Monitor 2</text>
                    <rect x="160" y="230" width="80" height="30" fill="#000" stroke="#333" stroke-width="1"/>
                    <text x="200" y="245" text-anchor="middle" font-size="8" fill="#0f0">SpO2: 98%</text>
                    <text x="200" y="255" text-anchor="middle" font-size="8" fill="#0f0">ETCO2: 35</text>
                    <text x="200" y="270" text-anchor="middle" font-size="9">Respiratory</text>
                </g>
                
                <!-- Specialized monitoring -->
                <g>
                    <text x="1000" y="80" text-anchor="middle" font-size="14" font-weight="bold">SPECIALIZED MONITORING</text>
                    
                    <!-- Hemodynamic -->
                    <rect x="950" y="100" width="100" height="50" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="1000" y="120" text-anchor="middle" font-size="11" font-weight="bold">Hemodynamic</text>
                    <text x="1000" y="135" text-anchor="middle" font-size="11" font-weight="bold">Monitoring</text>
                    
                    <!-- Neurological -->
                    <rect x="950" y="160" width="100" height="50" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="1000" y="180" text-anchor="middle" font-size="11" font-weight="bold">Neurological</text>
                    <text x="1000" y="195" text-anchor="middle" font-size="11" font-weight="bold">Monitoring</text>
                    
                    <!-- Metabolic -->
                    <rect x="950" y="220" width="100" height="50" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="1000" y="240" text-anchor="middle" font-size="11" font-weight="bold">Metabolic</text>
                    <text x="1000" y="255" text-anchor="middle" font-size="11" font-weight="bold">Monitoring</text>
                </g>
                
                <!-- Monitoring parameters -->
                <g>
                    <text x="600" y="320" text-anchor="middle" font-size="18" font-weight="bold">MONITORING PARAMETERS</text>
                    
                    <!-- Vital signs -->
                    <rect x="100" y="350" width="200" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="200" y="375" text-anchor="middle" font-size="14" font-weight="bold">VITAL SIGNS</text>
                    <text x="200" y="395" text-anchor="middle" font-size="11">• Heart rate (ECG)</text>
                    <text x="200" y="410" text-anchor="middle" font-size="11">• Blood pressure (NIBP/IBP)</text>
                    <text x="200" y="425" text-anchor="middle" font-size="11">• Oxygen saturation (SpO2)</text>
                    <text x="200" y="440" text-anchor="middle" font-size="11">• Temperature</text>
                    <text x="200" y="455" text-anchor="middle" font-size="11">• Respiratory rate</text>
                    
                    <!-- Respiratory -->
                    <rect x="320" y="350" width="200" height="120" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="420" y="375" text-anchor="middle" font-size="14" font-weight="bold">RESPIRATORY</text>
                    <text x="420" y="395" text-anchor="middle" font-size="11">• End-tidal CO2 (ETCO2)</text>
                    <text x="420" y="410" text-anchor="middle" font-size="11">• Airway pressure</text>
                    <text x="420" y="425" text-anchor="middle" font-size="11">• Tidal volume</text>
                    <text x="420" y="440" text-anchor="middle" font-size="11">• FiO2 monitoring</text>
                    <text x="420" y="455" text-anchor="middle" font-size="11">• Compliance/resistance</text>
                    
                    <!-- Hemodynamic -->
                    <rect x="540" y="350" width="200" height="120" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="640" y="375" text-anchor="middle" font-size="14" font-weight="bold">HEMODYNAMIC</text>
                    <text x="640" y="395" text-anchor="middle" font-size="11">• Central venous pressure</text>
                    <text x="640" y="410" text-anchor="middle" font-size="11">• Pulmonary artery pressure</text>
                    <text x="640" y="425" text-anchor="middle" font-size="11">• Cardiac output</text>
                    <text x="640" y="440" text-anchor="middle" font-size="11">• Stroke volume</text>
                    <text x="640" y="455" text-anchor="middle" font-size="11">• Systemic vascular resistance</text>
                    
                    <!-- Neurological -->
                    <rect x="760" y="350" width="200" height="120" fill="#ffcdd2" stroke="#f44336" stroke-width="3"/>
                    <text x="860" y="375" text-anchor="middle" font-size="14" font-weight="bold">NEUROLOGICAL</text>
                    <text x="860" y="395" text-anchor="middle" font-size="11">• Intracranial pressure (ICP)</text>
                    <text x="860" y="410" text-anchor="middle" font-size="11">• Cerebral perfusion pressure</text>
                    <text x="860" y="425" text-anchor="middle" font-size="11">• EEG monitoring</text>
                    <text x="860" y="440" text-anchor="middle" font-size="11">• Brain tissue oxygenation</text>
                    <text x="860" y="455" text-anchor="middle" font-size="11">• Pupillometry</text>
                </g>
                
                <!-- Alarm management -->
                <g>
                    <text x="200" y="520" font-size="16" font-weight="bold">ALARM MANAGEMENT</text>
                    <text x="200" y="545" font-size="12">• Intelligent alarm algorithms</text>
                    <text x="200" y="560" font-size="12">• Priority-based notifications</text>
                    <text x="200" y="575" font-size="12">• Customizable thresholds</text>
                    <text x="200" y="590" font-size="12">• Alarm fatigue reduction</text>
                    <text x="200" y="605" font-size="12">• Multi-modal alerts</text>
                </g>
                
                <!-- Data analytics -->
                <g>
                    <text x="600" y="520" font-size="16" font-weight="bold">DATA ANALYTICS</text>
                    <text x="600" y="545" font-size="12">• Real-time trend analysis</text>
                    <text x="600" y="560" font-size="12">• Predictive algorithms</text>
                    <text x="600" y="575" font-size="12">• Clinical decision support</text>
                    <text x="600" y="590" font-size="12">• Quality metrics tracking</text>
                    <text x="600" y="605" font-size="12">• Outcome correlation</text>
                </g>
                
                <!-- Connectivity -->
                <g>
                    <text x="1000" y="520" font-size="16" font-weight="bold">CONNECTIVITY</text>
                    <text x="1000" y="545" font-size="12">• Electronic health records</text>
                    <text x="1000" y="560" font-size="12">• Mobile device integration</text>
                    <text x="1000" y="575" font-size="12">• Telemedicine platforms</text>
                    <text x="1000" y="590" font-size="12">• Cloud-based storage</text>
                    <text x="1000" y="605" font-size="12">• Interoperability standards</text>
                </g>
                
                <!-- Data flow arrows -->
                <path d="M 250 140 L 550 140" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 650 140 L 950 140" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 180 L 600 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                
                <!-- Arrow marker -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>
