<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blockchain Technology: Comprehensive Study Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 3em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .subtitle {
            text-align: center;
            color: #666;
            font-size: 1.2em;
            margin-bottom: 40px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        .question-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        .question-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .question-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .question-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .question-description {
            color: #666;
            font-size: 0.95em;
            margin-bottom: 15px;
        }
        .question-topics {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .topic-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
        .intro-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
            border-left: 5px solid #2196f3;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            border-top: 1px solid #dee2e6;
        }
        a {
            text-decoration: none;
            color: inherit;
        }
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Blockchain Technology Study Guide</h1>
        <p class="subtitle">Comprehensive exploration of distributed systems, cryptography, and blockchain fundamentals</p>
        
        <div class="intro-section">
            <h2>📚 About This Study Guide</h2>
            <p>This comprehensive study guide covers 10 fundamental topics in blockchain technology and distributed systems. Each question includes detailed explanations, interactive block diagrams, real-world examples, and practical applications.</p>
            
            <p><strong>What you'll learn:</strong></p>
            <ul>
                <li>Core blockchain concepts and their mathematical foundations</li>
                <li>Distributed systems principles and fault tolerance mechanisms</li>
                <li>Cryptographic techniques used in modern blockchain systems</li>
                <li>Privacy-preserving technologies and zero-knowledge proofs</li>
                <li>Mining algorithms and ASIC resistance strategies</li>
            </ul>
        </div>

        <div class="grid">
            <a href="question_1_byzantine_generals_problem.html">
                <div class="question-card">
                    <div class="question-number">01</div>
                    <div class="question-title"><span class="emoji">⚔️</span>Byzantine Generals Problem</div>
                    <div class="question-description">
                        Explore the fundamental problem of achieving consensus in distributed systems with potentially malicious actors, and how blockchain solves it.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Consensus</span>
                        <span class="topic-tag">Fault Tolerance</span>
                        <span class="topic-tag">Distributed Systems</span>
                    </div>
                </div>
            </a>

            <a href="question_2_distributed_databases.html">
                <div class="question-card">
                    <div class="question-number">02</div>
                    <div class="question-title"><span class="emoji">🗄️</span>Distributed Databases</div>
                    <div class="question-description">
                        Understand how distributed databases work and their relationship to blockchain technology, including CAP theorem and consistency models.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Databases</span>
                        <span class="topic-tag">CAP Theorem</span>
                        <span class="topic-tag">Replication</span>
                    </div>
                </div>
            </a>

            <a href="question_3_hdfs_decentralized_storage.html">
                <div class="question-card">
                    <div class="question-number">03</div>
                    <div class="question-title"><span class="emoji">📁</span>HDFS & Decentralized Storage</div>
                    <div class="question-description">
                        Learn about Hadoop Distributed File System and its role in modern decentralized storage solutions and big data processing.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Storage</span>
                        <span class="topic-tag">HDFS</span>
                        <span class="topic-tag">Big Data</span>
                    </div>
                </div>
            </a>

            <a href="question_4_asic_resistance.html">
                <div class="question-card">
                    <div class="question-number">04</div>
                    <div class="question-title"><span class="emoji">🔌</span>ASIC Resistance</div>
                    <div class="question-description">
                        Discover why ASIC resistance is important for blockchain decentralization and the techniques used to maintain mining accessibility.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Mining</span>
                        <span class="topic-tag">Decentralization</span>
                        <span class="topic-tag">Hardware</span>
                    </div>
                </div>
            </a>

            <a href="question_5_distributed_hash_table.html">
                <div class="question-card">
                    <div class="question-number">05</div>
                    <div class="question-title"><span class="emoji">🗂️</span>Distributed Hash Tables</div>
                    <div class="question-description">
                        Explore DHTs and their importance in decentralized systems, including peer-to-peer networks and blockchain node discovery.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">P2P Networks</span>
                        <span class="topic-tag">DHT</span>
                        <span class="topic-tag">Routing</span>
                    </div>
                </div>
            </a>

            <a href="question_6_fault_tolerance.html">
                <div class="question-card">
                    <div class="question-number">06</div>
                    <div class="question-title"><span class="emoji">🛡️</span>Fault Tolerance</div>
                    <div class="question-description">
                        Understand fault tolerance mechanisms in blockchain systems and how they ensure reliability despite node failures and attacks.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Reliability</span>
                        <span class="topic-tag">Byzantine Faults</span>
                        <span class="topic-tag">Recovery</span>
                    </div>
                </div>
            </a>

            <a href="question_7_cryptographic_hash_functions.html">
                <div class="question-card">
                    <div class="question-number">07</div>
                    <div class="question-title"><span class="emoji">🔐</span>Cryptographic Hash Functions</div>
                    <div class="question-description">
                        Learn about hash functions and their critical role in blockchain security, integrity verification, and mining processes.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Cryptography</span>
                        <span class="topic-tag">Security</span>
                        <span class="topic-tag">Integrity</span>
                    </div>
                </div>
            </a>

            <a href="question_8_digital_signatures_ecdsa.html">
                <div class="question-card">
                    <div class="question-number">08</div>
                    <div class="question-title"><span class="emoji">✍️</span>Digital Signatures & ECDSA</div>
                    <div class="question-description">
                        Discover how digital signatures using ECDSA provide authentication and non-repudiation in blockchain transactions.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Authentication</span>
                        <span class="topic-tag">ECDSA</span>
                        <span class="topic-tag">Transactions</span>
                    </div>
                </div>
            </a>

            <a href="question_9_zero_knowledge_proofs.html">
                <div class="question-card">
                    <div class="question-number">09</div>
                    <div class="question-title"><span class="emoji">🔍</span>Zero-Knowledge Proofs</div>
                    <div class="question-description">
                        Explore zero-knowledge proofs and their applications in privacy-preserving blockchain systems and scalability solutions.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Privacy</span>
                        <span class="topic-tag">ZK-SNARKs</span>
                        <span class="topic-tag">Scalability</span>
                    </div>
                </div>
            </a>

            <a href="question_10_memory_hard_algorithms.html">
                <div class="question-card">
                    <div class="question-number">10</div>
                    <div class="question-title"><span class="emoji">🧠</span>Memory Hard Algorithms</div>
                    <div class="question-description">
                        Understand memory hard algorithms and their role in maintaining ASIC resistance and mining decentralization.
                    </div>
                    <div class="question-topics">
                        <span class="topic-tag">Memory</span>
                        <span class="topic-tag">ASIC Resistance</span>
                        <span class="topic-tag">Algorithms</span>
                    </div>
                </div>
            </a>
        </div>

        <div class="footer">
            <p><strong>Study Guide Features:</strong></p>
            <p>✅ Interactive block diagrams • ✅ Real-world examples • ✅ Comprehensive explanations • ✅ Advantages & disadvantages analysis</p>
            <p>Each topic includes detailed technical explanations, visual diagrams, and practical applications in modern blockchain systems.</p>
        </div>
    </div>
</body>
</html>
