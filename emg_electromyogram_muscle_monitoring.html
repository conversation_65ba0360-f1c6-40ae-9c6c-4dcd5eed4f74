<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EMG Electromyogram - Muscle Activity Monitoring System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .clinical-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💪 EMG Electromyogram - Muscle Activity Monitoring System</h1>

        <div class="overview-section">
            <h2>Overview of Electromyography</h2>
            <p>The electromyogram (EMG) is a specialized biomedical technique that records and analyzes the electrical activity generated by skeletal muscles during contraction and relaxation. This diagnostic tool captures action potentials from muscle fibers through surface or needle electrodes, providing crucial information about muscle function, neuromuscular disorders, motor unit recruitment, and muscle fatigue. Modern EMG systems integrate sophisticated signal processing, real-time analysis, and advanced filtering to distinguish between normal and pathological muscle activity, making them essential in neurology, rehabilitation medicine, sports science, and ergonomic assessment.</p>
        </div>

        <div class="section-title">🔧 EMG System Block Diagram & Architecture</div>

        <div class="diagram-container">
            <svg width="100%" height="800" viewBox="0 0 1200 800">
                <!-- Title -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">EMG ELECTROMYOGRAM SYSTEM BLOCK DIAGRAM</text>

                <!-- Patient and muscle -->
                <g>
                    <text x="150" y="80" text-anchor="middle" font-size="14" font-weight="bold">PATIENT INTERFACE</text>

                    <!-- Muscle representation -->
                    <ellipse cx="150" cy="140" rx="40" ry="60" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="150" y="145" text-anchor="middle" font-size="12" font-weight="bold">MUSCLE</text>

                    <!-- Surface electrodes -->
                    <circle cx="120" cy="120" r="8" fill="#ff9800"/>
                    <text x="120" y="125" text-anchor="middle" font-size="6" fill="white">+</text>
                    <circle cx="180" cy="120" r="8" fill="#ff9800"/>
                    <text x="180" y="125" text-anchor="middle" font-size="6" fill="white">-</text>
                    <circle cx="150" cy="180" r="8" fill="#9c27b0"/>
                    <text x="150" y="185" text-anchor="middle" font-size="6" fill="white">GND</text>

                    <!-- Needle electrode option -->
                    <line x1="130" y1="140" x2="140" y2="140" stroke="#333" stroke-width="2"/>
                    <circle cx="125" cy="140" r="3" fill="#666"/>

                    <text x="150" y="210" text-anchor="middle" font-size="10">Surface/Needle Electrodes</text>
                    <text x="150" y="225" text-anchor="middle" font-size="10">Bipolar Configuration</text>
                </g>

                <!-- Electrode selector -->
                <g>
                    <rect x="280" y="100" width="120" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="340" y="125" text-anchor="middle" font-size="12" font-weight="bold">ELECTRODE</text>
                    <text x="340" y="140" text-anchor="middle" font-size="12" font-weight="bold">INTERFACE</text>
                    <text x="340" y="155" text-anchor="middle" font-size="10">• Multi-channel input</text>
                    <text x="340" y="170" text-anchor="middle" font-size="10">• Impedance matching</text>
                </g>

                <!-- Pre-amplifier -->
                <g>
                    <rect x="450" y="100" width="120" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="510" y="125" text-anchor="middle" font-size="12" font-weight="bold">PRE-AMPLIFIER</text>
                    <text x="510" y="140" text-anchor="middle" font-size="10">• Gain: 100-1000x</text>
                    <text x="510" y="155" text-anchor="middle" font-size="10">• High input impedance</text>
                    <text x="510" y="170" text-anchor="middle" font-size="10">• Low noise: <5μV</text>
                </g>

                <!-- Filter bank -->
                <g>
                    <rect x="620" y="100" width="120" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="680" y="125" text-anchor="middle" font-size="12" font-weight="bold">FILTER BANK</text>
                    <text x="680" y="140" text-anchor="middle" font-size="10">• High-pass: 10 Hz</text>
                    <text x="680" y="155" text-anchor="middle" font-size="10">• Low-pass: 500 Hz</text>
                    <text x="680" y="170" text-anchor="middle" font-size="10">• Notch: 50/60 Hz</text>
                </g>

                <!-- Main amplifier -->
                <g>
                    <rect x="790" y="100" width="120" height="80" fill="#bbdefb" stroke="#2196f3" stroke-width="3"/>
                    <text x="850" y="125" text-anchor="middle" font-size="12" font-weight="bold">MAIN AMPLIFIER</text>
                    <text x="850" y="140" text-anchor="middle" font-size="10">• Variable gain</text>
                    <text x="850" y="155" text-anchor="middle" font-size="10">• Total gain: 10,000x</text>
                    <text x="850" y="170" text-anchor="middle" font-size="10">• Output: ±5V</text>
                </g>

                <!-- ADC -->
                <g>
                    <rect x="960" y="100" width="120" height="80" fill="#dcedc8" stroke="#689f38" stroke-width="3"/>
                    <text x="1020" y="125" text-anchor="middle" font-size="12" font-weight="bold">ADC CONVERTER</text>
                    <text x="1020" y="140" text-anchor="middle" font-size="10">• 16-24 bit resolution</text>
                    <text x="1020" y="155" text-anchor="middle" font-size="10">• 2-10 kHz sampling</text>
                    <text x="1020" y="170" text-anchor="middle" font-size="10">• Multi-channel</text>
                </g>

                <!-- Signal flow arrows -->
                <path d="M 200 140 L 280 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 400 140 L 450 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 570 140 L 620 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 740 140 L 790 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 910 140 L 960 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                <!-- Digital processing unit -->
                <g>
                    <rect x="500" y="250" width="200" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="600" y="280" text-anchor="middle" font-size="14" font-weight="bold">DIGITAL SIGNAL PROCESSOR</text>
                    <text x="600" y="300" text-anchor="middle" font-size="11">• RMS calculation</text>
                    <text x="600" y="315" text-anchor="middle" font-size="11">• Frequency analysis (FFT)</text>
                    <text x="600" y="330" text-anchor="middle" font-size="11">• Fatigue analysis</text>
                    <text x="600" y="345" text-anchor="middle" font-size="11">• Pattern recognition</text>
                </g>

                <!-- Output systems -->
                <g>
                    <text x="600" y="400" text-anchor="middle" font-size="16" font-weight="bold">OUTPUT & ANALYSIS SYSTEMS</text>

                    <!-- Real-time display -->
                    <rect x="100" y="430" width="200" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                    <text x="200" y="455" text-anchor="middle" font-size="12" font-weight="bold">REAL-TIME DISPLAY</text>
                    <rect x="120" y="470" width="160" height="60" fill="#000" stroke="#333" stroke-width="1"/>
                    <!-- EMG waveform -->
                    <path d="M 130 500 L 135 495 L 140 505 L 145 490 L 150 510 L 155 485 L 160 515 L 165 480 L 170 520 L 175 485 L 180 515 L 185 490 L 190 505 L 195 495 L 200 500 L 205 505 L 210 495 L 215 500 L 220 505 L 225 495 L 230 500 L 235 505 L 240 495 L 245 500 L 250 505 L 255 495 L 260 500 L 265 505 L 270 495 L 275 500"
                          stroke="#0f0" stroke-width="1" fill="none"/>
                    <text x="200" y="545" text-anchor="middle" font-size="10">Raw EMG signal</text>

                    <!-- Spectral analysis -->
                    <rect x="350" y="430" width="200" height="120" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="450" y="455" text-anchor="middle" font-size="12" font-weight="bold">SPECTRAL ANALYSIS</text>
                    <rect x="370" y="470" width="160" height="60" fill="#000" stroke="#333" stroke-width="1"/>
                    <!-- Frequency spectrum -->
                    <rect x="380" y="520" width="8" height="10" fill="#ff0000"/>
                    <rect x="390" y="515" width="8" height="15" fill="#ff4500"/>
                    <rect x="400" y="510" width="8" height="20" fill="#ffa500"/>
                    <rect x="410" y="505" width="8" height="25" fill="#ffff00"/>
                    <rect x="420" y="500" width="8" height="30" fill="#9acd32"/>
                    <rect x="430" y="505" width="8" height="25" fill="#00ff00"/>
                    <rect x="440" y="510" width="8" height="20" fill="#00ffff"/>
                    <rect x="450" y="515" width="8" height="15" fill="#0000ff"/>
                    <rect x="460" y="520" width="8" height="10" fill="#8a2be2"/>
                    <rect x="470" y="525" width="8" height="5" fill="#ff1493"/>
                    <rect x="480" y="520" width="8" height="10" fill="#32cd32"/>
                    <rect x="490" y="515" width="8" height="15" fill="#ff6347"/>
                    <rect x="500" y="520" width="8" height="10" fill="#4169e1"/>
                    <rect x="510" y="525" width="8" height="5" fill="#ff69b4"/>
                    <text x="450" y="545" text-anchor="middle" font-size="10">Power spectrum</text>

                    <!-- Computer analysis -->
                    <rect x="600" y="430" width="200" height="120" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="700" y="455" text-anchor="middle" font-size="12" font-weight="bold">COMPUTER ANALYSIS</text>
                    <text x="700" y="475" text-anchor="middle" font-size="10">• RMS amplitude</text>
                    <text x="700" y="490" text-anchor="middle" font-size="10">• Mean/median frequency</text>
                    <text x="700" y="505" text-anchor="middle" font-size="10">• Fatigue indices</text>
                    <text x="700" y="520" text-anchor="middle" font-size="10">• Statistical analysis</text>
                    <text x="700" y="535" text-anchor="middle" font-size="10">• Report generation</text>

                    <!-- Biofeedback -->
                    <rect x="850" y="430" width="200" height="120" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="950" y="455" text-anchor="middle" font-size="12" font-weight="bold">BIOFEEDBACK</text>
                    <circle cx="950" cy="485" r="20" fill="#4caf50"/>
                    <text x="950" y="490" text-anchor="middle" font-size="10" fill="white">ACTIVE</text>
                    <text x="950" y="515" text-anchor="middle" font-size="10">• Visual feedback</text>
                    <text x="950" y="530" text-anchor="middle" font-size="10">• Audio feedback</text>
                    <text x="950" y="545" text-anchor="middle" font-size="10">• Training protocols</text>
                </g>

                <!-- Connection lines -->
                <path d="M 1020 180 L 1020 220 L 600 220 L 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 200 350 L 200 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 450 350 L 450 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 700 350 L 700 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 350 L 950 350 L 950 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                <!-- Power and calibration systems -->
                <g>
                    <text x="200" y="600" font-size="14" font-weight="bold">POWER SUPPLY</text>
                    <rect x="100" y="620" width="200" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="200" y="645" text-anchor="middle" font-size="11">• Low noise design</text>
                    <text x="200" y="660" text-anchor="middle" font-size="11">• Battery operation</text>
                    <text x="200" y="675" text-anchor="middle" font-size="11">• Isolated circuits</text>
                    <text x="200" y="690" text-anchor="middle" font-size="11">• EMI protection</text>
                </g>

                <g>
                    <text x="600" y="600" font-size="14" font-weight="bold">CALIBRATION SYSTEM</text>
                    <rect x="500" y="620" width="200" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="600" y="645" text-anchor="middle" font-size="11">• Signal generator</text>
                    <text x="600" y="660" text-anchor="middle" font-size="11">• Amplitude calibration</text>
                    <text x="600" y="675" text-anchor="middle" font-size="11">• Frequency response</text>
                    <text x="600" y="690" text-anchor="middle" font-size="11">• System verification</text>
                </g>

                <g>
                    <text x="1000" y="600" font-size="14" font-weight="bold">SAFETY SYSTEMS</text>
                    <rect x="900" y="620" width="200" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="1000" y="645" text-anchor="middle" font-size="11">• Patient isolation</text>
                    <text x="1000" y="660" text-anchor="middle" font-size="11">• Overload protection</text>
                    <text x="1000" y="675" text-anchor="middle" font-size="11">• Ground monitoring</text>
                    <text x="1000" y="690" text-anchor="middle" font-size="11">• Emergency shutdown</text>
                </g>

                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">💪 Muscle Activity Patterns & Recording Techniques</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>🔴 Surface EMG (sEMG)</h3>
                <p><strong>Electrode Type:</strong> Ag/AgCl surface electrodes</p>
                <p><strong>Placement:</strong> Over muscle belly, bipolar configuration</p>
                <p><strong>Inter-electrode Distance:</strong> 20-25 mm</p>
                <p><strong>Signal Amplitude:</strong> 50-5000 μV</p>
                <p><strong>Frequency Range:</strong> 10-500 Hz</p>
                <p><strong>Applications:</strong> Gait analysis, sports science, rehabilitation</p>
                <p><strong>Advantages:</strong> Non-invasive, easy application</p>
            </div>

            <div class="component-card">
                <h3>🔵 Needle EMG (nEMG)</h3>
                <p><strong>Electrode Type:</strong> Concentric or monopolar needles</p>
                <p><strong>Placement:</strong> Intramuscular insertion</p>
                <p><strong>Signal Amplitude:</strong> 100-10,000 μV</p>
                <p><strong>Frequency Range:</strong> 2-10,000 Hz</p>
                <p><strong>Applications:</strong> Neuromuscular diagnosis, motor unit analysis</p>
                <p><strong>Advantages:</strong> High spatial resolution, specific muscle targeting</p>
                <p><strong>Disadvantages:</strong> Invasive, requires expertise</p>
            </div>

            <div class="component-card">
                <h3>🟡 Motor Unit Action Potentials (MUAPs)</h3>
                <p><strong>Duration:</strong> 5-15 ms (normal)</p>
                <p><strong>Amplitude:</strong> 200-2000 μV</p>
                <p><strong>Phases:</strong> 2-4 phases (normal)</p>
                <p><strong>Recruitment:</strong> Size principle (small to large)</p>
                <p><strong>Firing Rate:</strong> 8-50 Hz</p>
                <p><strong>Pathology:</strong> Increased duration/amplitude in neuropathy</p>
                <p><strong>Analysis:</strong> Template matching, decomposition</p>
            </div>

            <div class="component-card">
                <h3>🟠 Muscle Fatigue Analysis</h3>
                <p><strong>Amplitude Changes:</strong> Increase during fatigue</p>
                <p><strong>Frequency Shift:</strong> Median frequency decreases</p>
                <p><strong>Conduction Velocity:</strong> Decreases with fatigue</p>
                <p><strong>Spectral Compression:</strong> Power shifts to lower frequencies</p>
                <p><strong>Fatigue Index:</strong> Rate of frequency decline</p>
                <p><strong>Applications:</strong> Ergonomics, sports performance</p>
            </div>

            <div class="component-card">
                <h3>⚡ Signal Processing Techniques</h3>
                <p><strong>Rectification:</strong> Full-wave rectification</p>
                <p><strong>Smoothing:</strong> RMS or moving average</p>
                <p><strong>Filtering:</strong> Bandpass 10-500 Hz</p>
                <p><strong>Normalization:</strong> %MVC (Maximum Voluntary Contraction)</p>
                <p><strong>Frequency Analysis:</strong> FFT, power spectral density</p>
                <p><strong>Time-Frequency:</strong> Wavelet analysis, STFT</p>
            </div>

            <div class="component-card">
                <h3>📍 Electrode Placement Guidelines</h3>
                <p><strong>Skin Preparation:</strong> Clean, shave, light abrasion</p>
                <p><strong>Electrode Orientation:</strong> Parallel to muscle fibers</p>
                <p><strong>Reference Electrode:</strong> Over electrically neutral tissue</p>
                <p><strong>Impedance:</strong> <5 kΩ for surface electrodes</p>
                <p><strong>Fixation:</strong> Secure attachment, avoid movement</p>
                <p><strong>Documentation:</strong> Anatomical landmarks, photos</p>
            </div>
        </div>

        <div class="section-title">🏥 Clinical Applications & Diagnostic Uses</div>

        <div class="clinical-section">
            <h2>EMG in Clinical Practice</h2>
            <p>EMG is essential in diagnosing neuromuscular disorders, assessing muscle function, monitoring rehabilitation progress, and evaluating motor control. The technique provides valuable information about muscle activation patterns, motor unit recruitment, neuromuscular fatigue, and the effectiveness of therapeutic interventions in both clinical and research settings.</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Clinical Application</th>
                    <th>EMG Type</th>
                    <th>Key Parameters</th>
                    <th>Diagnostic Value</th>
                    <th>Normal Values</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Neuromuscular Disorders</strong></td>
                    <td>Needle EMG</td>
                    <td>MUAP morphology, recruitment</td>
                    <td>Differentiate myopathy vs neuropathy</td>
                    <td>MUAP: 5-15ms, 200-2000μV</td>
                </tr>
                <tr>
                    <td><strong>Gait Analysis</strong></td>
                    <td>Surface EMG</td>
                    <td>Activation timing, amplitude</td>
                    <td>Movement pattern assessment</td>
                    <td>Varies by muscle and activity</td>
                </tr>
                <tr>
                    <td><strong>Muscle Fatigue</strong></td>
                    <td>Surface EMG</td>
                    <td>Median frequency, RMS amplitude</td>
                    <td>Fatigue rate, endurance capacity</td>
                    <td>MF: 50-150 Hz (muscle dependent)</td>
                </tr>
                <tr>
                    <td><strong>Biofeedback Training</strong></td>
                    <td>Surface EMG</td>
                    <td>Real-time amplitude</td>
                    <td>Motor learning, rehabilitation</td>
                    <td>Target-specific thresholds</td>
                </tr>
                <tr>
                    <td><strong>Ergonomic Assessment</strong></td>
                    <td>Surface EMG</td>
                    <td>%MVC, activation patterns</td>
                    <td>Workplace risk evaluation</td>
                    <td><20% MVC for sustained work</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">⚙️ Technical Specifications & Maintenance</div>

        <div class="dual-column">
            <div class="clinical-section">
                <h3>System Specifications</h3>
                <ul>
                    <li><span class="highlight">Input Range:</span> ±10 mV full scale</li>
                    <li><span class="highlight">Resolution:</span> 16-24 bit ADC</li>
                    <li><span class="highlight">Sampling Rate:</span> 1-10 kHz per channel</li>
                    <li><span class="highlight">Frequency Response:</span> 10-500 Hz (±3dB)</li>
                    <li><span class="highlight">CMRR:</span> >100 dB at 50/60 Hz</li>
                    <li><span class="highlight">Input Impedance:</span> >100 MΩ</li>
                    <li><span class="highlight">Noise Level:</span> <5 μV RMS</li>
                    <li><span class="highlight">Channels:</span> 1-64 channels</li>
                </ul>
            </div>

            <div class="clinical-section">
                <h3>Calibration & Maintenance</h3>
                <ul>
                    <li><span class="highlight">Daily:</span> System check, electrode inspection</li>
                    <li><span class="highlight">Weekly:</span> Cable testing, impedance verification</li>
                    <li><span class="highlight">Monthly:</span> Calibration verification, signal quality</li>
                    <li><span class="highlight">Quarterly:</span> Full system validation</li>
                    <li><span class="highlight">Annually:</strong> Professional service and certification</li>
                    <li><span class="highlight">Electrode Care:</span> Proper cleaning, storage</li>
                    <li><span class="highlight">Cable Management:</span> Avoid stress, kinking</li>
                    <li><span class="highlight">Documentation:</span> Maintenance logs, calibration records</li>
                </ul>
            </div>
        </div>

        <div class="section-title">🔧 Troubleshooting & Safety Protocols</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>⚠️ Common Issues & Solutions</h3>
                <p><strong>High Noise Level:</strong> Check grounding, electrode contact</p>
                <p><strong>Motion Artifacts:</strong> Secure electrode placement, cable management</p>
                <p><strong>Baseline Drift:</strong> Verify electrode impedance, skin preparation</p>
                <p><strong>Cross-talk:</strong> Increase inter-electrode distance</p>
                <p><strong>Saturation:</strong> Reduce gain, check electrode placement</p>
                <p><strong>Poor Signal Quality:</strong> Fresh electrodes, skin preparation</p>
            </div>

            <div class="component-card">
                <h3>🛡️ Safety & Compliance</h3>
                <p><strong>Electrical Safety:</strong> IEC 60601-1 compliance</p>
                <p><strong>Patient Isolation:</strong> Medical grade isolation</p>
                <p><strong>Infection Control:</strong> Disposable electrodes, sterilization</p>
                <p><strong>Needle Safety:</strong> Proper disposal, universal precautions</p>
                <p><strong>EMC Standards:</strong> IEC 60601-1-2 compliance</p>
                <p><strong>Data Security:</strong> HIPAA compliant storage</p>
            </div>

            <div class="component-card">
                <h3>📊 Quality Assurance</h3>
                <p><strong>Signal Validation:</strong> Real-time quality metrics</p>
                <p><strong>Artifact Detection:</strong> Automated algorithms</p>
                <p><strong>Data Integrity:</strong> Digital signatures, checksums</p>
                <p><strong>Calibration Tracking:</strong> Automated verification</p>
                <p><strong>Backup Systems:</strong> Redundant data storage</p>
                <p><strong>Audit Trails:</strong> Complete recording history</p>
            </div>
        </div>

    </div>
</body>
</html>
