<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question 5: Distributed Hash Table (DHT) in Decentralized Systems</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #667eea;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .concept-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        .advantages-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
        .disadvantages-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
        }
        .highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #856404;
        }
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ Question 5: Distributed Hash Table (DHT) & Its Importance in Decentralized Systems</h1>

        <div class="section">
            <h2><span class="emoji">📚</span>What is a Distributed Hash Table?</h2>

            <div class="concept-box">
                <h3>Definition and Core Principles</h3>
                <p>A <span class="highlight">Distributed Hash Table (DHT)</span> is a decentralized distributed system that provides a lookup service similar to a hash table. It stores key-value pairs across multiple nodes in a network, allowing efficient retrieval without central coordination or single points of failure.</p>

                <p><strong>Key Characteristics:</strong></p>
                <ul>
                    <li><strong>Decentralization:</strong> No central authority or coordinator</li>
                    <li><strong>Scalability:</strong> Can handle millions of nodes efficiently</li>
                    <li><strong>Fault Tolerance:</strong> Continues operating despite node failures</li>
                    <li><strong>Self-Organization:</strong> Nodes automatically organize themselves</li>
                    <li><strong>Efficient Lookup:</strong> Logarithmic time complexity O(log N)</li>
                </ul>
            </div>

            <div class="diagram-container">
                <svg width="100%" height="700" viewBox="0 0 1200 700">
                    <!-- Title -->
                    <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold">DHT ARCHITECTURE & OPERATION</text>

                    <!-- Centralized vs DHT Comparison -->
                    <text x="200" y="70" text-anchor="middle" font-size="16" font-weight="bold">CENTRALIZED HASH TABLE</text>
                    <text x="800" y="70" text-anchor="middle" font-size="16" font-weight="bold">DISTRIBUTED HASH TABLE</text>

                    <!-- Centralized System -->
                    <g>
                        <!-- Central Server -->
                        <rect x="150" y="100" width="100" height="80" fill="#FFCDD2" stroke="#F44336" stroke-width="3"/>
                        <text x="200" y="130" text-anchor="middle" font-size="12" font-weight="bold">CENTRAL</text>
                        <text x="200" y="145" text-anchor="middle" font-size="12" font-weight="bold">SERVER</text>
                        <text x="200" y="160" text-anchor="middle" font-size="10">All Data</text>

                        <!-- Clients -->
                        <circle cx="100" cy="220" r="15" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="100" y="225" text-anchor="middle" font-size="8">C1</text>

                        <circle cx="200" cy="220" r="15" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="200" y="225" text-anchor="middle" font-size="8">C2</text>

                        <circle cx="300" cy="220" r="15" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="300" y="225" text-anchor="middle" font-size="8">C3</text>

                        <!-- Connections -->
                        <line x1="115" y1="210" x2="185" y2="180" stroke="#2196F3" stroke-width="2"/>
                        <line x1="200" y1="200" x2="200" y2="180" stroke="#2196F3" stroke-width="2"/>
                        <line x1="285" y1="210" x2="215" y2="180" stroke="#2196F3" stroke-width="2"/>

                        <text x="200" y="260" text-anchor="middle" font-size="10" fill="#F44336">Single Point of Failure</text>
                    </g>

                    <!-- DHT System -->
                    <g>
                        <!-- DHT Ring -->
                        <circle cx="800" cy="150" r="80" fill="none" stroke="#4CAF50" stroke-width="3"/>

                        <!-- Nodes on the ring -->
                        <circle cx="800" cy="70" r="20" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="800" y="75" text-anchor="middle" font-size="10" font-weight="bold">N1</text>
                        <text x="800" y="50" text-anchor="middle" font-size="8">ID: 0</text>

                        <circle cx="880" cy="150" r="20" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="880" y="155" text-anchor="middle" font-size="10" font-weight="bold">N2</text>
                        <text x="900" y="140" text-anchor="middle" font-size="8">ID: 64</text>

                        <circle cx="800" cy="230" r="20" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="800" y="235" text-anchor="middle" font-size="10" font-weight="bold">N3</text>
                        <text x="800" y="250" text-anchor="middle" font-size="8">ID: 128</text>

                        <circle cx="720" cy="150" r="20" fill="#C8E6C9" stroke="#4CAF50" stroke-width="2"/>
                        <text x="720" y="155" text-anchor="middle" font-size="10" font-weight="bold">N4</text>
                        <text x="700" y="140" text-anchor="middle" font-size="8">ID: 192</text>

                        <!-- Data distribution -->
                        <text x="830" y="100" font-size="8">Keys: 0-63</text>
                        <text x="900" y="180" font-size="8">Keys: 64-127</text>
                        <text x="820" y="210" font-size="8">Keys: 128-191</text>
                        <text x="670" y="180" font-size="8">Keys: 192-255</text>

                        <text x="800" y="280" text-anchor="middle" font-size="10" fill="#4CAF50">No Single Point of Failure</text>
                    </g>

                    <!-- DHT Lookup Process -->
                    <text x="600" y="330" text-anchor="middle" font-size="16" font-weight="bold">DHT LOOKUP PROCESS</text>

                    <!-- Step-by-step lookup -->
                    <g>
                        <!-- Query -->
                        <rect x="100" y="360" width="120" height="40" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                        <text x="160" y="380" text-anchor="middle" font-size="12" font-weight="bold">QUERY</text>
                        <text x="160" y="395" text-anchor="middle" font-size="10">Find Key: 100</text>

                        <!-- Hash Function -->
                        <rect x="280" y="360" width="120" height="40" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
                        <text x="340" y="380" text-anchor="middle" font-size="12" font-weight="bold">HASH</text>
                        <text x="340" y="395" text-anchor="middle" font-size="10">Hash(100) = 100</text>

                        <!-- Routing -->
                        <rect x="460" y="360" width="120" height="40" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
                        <text x="520" y="380" text-anchor="middle" font-size="12" font-weight="bold">ROUTING</text>
                        <text x="520" y="395" text-anchor="middle" font-size="10">Route to Node 2</text>

                        <!-- Result -->
                        <rect x="640" y="360" width="120" height="40" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="700" y="380" text-anchor="middle" font-size="12" font-weight="bold">RESULT</text>
                        <text x="700" y="395" text-anchor="middle" font-size="10">Value Found</text>

                        <!-- Arrows -->
                        <path d="M 220 380 L 280 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 400 380 L 460 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                        <path d="M 580 380 L 640 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                    </g>

                    <!-- Chord DHT Example -->
                    <text x="600" y="450" text-anchor="middle" font-size="16" font-weight="bold">CHORD DHT EXAMPLE</text>

                    <!-- Chord ring with finger tables -->
                    <g>
                        <circle cx="600" cy="520" r="60" fill="none" stroke="#9C27B0" stroke-width="2"/>

                        <!-- Nodes -->
                        <circle cx="600" cy="460" r="15" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="600" y="465" text-anchor="middle" font-size="9" font-weight="bold">0</text>

                        <circle cx="652" cy="490" r="15" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="652" y="495" text-anchor="middle" font-size="9" font-weight="bold">1</text>

                        <circle cx="652" cy="550" r="15" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="652" y="555" text-anchor="middle" font-size="9" font-weight="bold">3</text>

                        <circle cx="548" cy="550" r="15" fill="#E1BEE7" stroke="#9C27B0" stroke-width="2"/>
                        <text x="548" y="555" text-anchor="middle" font-size="9" font-weight="bold">6</text>

                        <!-- Finger table for node 0 -->
                        <rect x="450" y="460" width="100" height="60" fill="#F3E5F5" stroke="#9C27B0" stroke-width="1"/>
                        <text x="500" y="475" text-anchor="middle" font-size="10" font-weight="bold">Finger Table (0)</text>
                        <text x="460" y="490" font-size="8">+1 → 1</text>
                        <text x="460" y="500" font-size="8">+2 → 3</text>
                        <text x="460" y="510" font-size="8">+4 → 6</text>

                        <!-- Lookup path -->
                        <path d="M 600 460 L 652 490" stroke="#F44336" stroke-width="2" stroke-dasharray="5,5"/>
                        <path d="M 652 490 L 652 550" stroke="#F44336" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="680" y="520" font-size="9" fill="#F44336">Lookup path</text>
                    </g>

                    <!-- Key Features -->
                    <text x="100" y="620" font-size="12" font-weight="bold">Key Features:</text>
                    <text x="100" y="635" font-size="10">• O(log N) lookup time</text>
                    <text x="250" y="635" font-size="10">• Self-organizing</text>
                    <text x="350" y="635" font-size="10">• Fault tolerant</text>
                    <text x="450" y="635" font-size="10">• Load balancing</text>

                    <text x="100" y="655" font-size="12" font-weight="bold">Applications:</text>
                    <text x="100" y="670" font-size="10">• BitTorrent</text>
                    <text x="180" y="670" font-size="10">• IPFS</text>
                    <text x="230" y="670" font-size="10">• Ethereum</text>
                    <text x="300" y="670" font-size="10">• Kademlia</text>
                    <text x="380" y="670" font-size="10">• Chord</text>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">⚙️</span>How DHT Works</h2>

            <div class="example-box">
                <h3>DHT Operation Process</h3>
                <div class="code-snippet">
1. KEY-VALUE STORAGE:
   hash(key) → node_id
   Store (key, value) at node responsible for hash(key)

2. LOOKUP PROCESS:
   Client: "Find value for key K"
   hash(K) → target_node_id
   Route query through network to target_node_id
   Return value if found

3. NODE JOIN:
   New node gets ID (usually hash of IP address)
   Contacts bootstrap node to join network
   Updates routing tables of existing nodes
   Takes responsibility for subset of keys

4. NODE DEPARTURE:
   Transfers keys to successor nodes
   Updates routing tables
   Network self-heals automatically
                </div>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">✅</span>Advantages of DHT</h2>

            <div class="advantages-box">
                <h3>Key Benefits</h3>
                <ul>
                    <li><strong>Scalability:</strong> Logarithmic lookup time even with millions of nodes</li>
                    <li><strong>Fault Tolerance:</strong> Automatic recovery from node failures</li>
                    <li><strong>Load Distribution:</strong> Data and queries distributed evenly</li>
                    <li><strong>Self-Healing:</strong> Network automatically repairs itself</li>
                    <li><strong>No Central Authority:</strong> Truly decentralized operation</li>
                    <li><strong>Efficient Storage:</strong> Optimal use of available storage across nodes</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">❌</span>Disadvantages and Challenges</h2>

            <div class="disadvantages-box">
                <h3>Limitations</h3>
                <ul>
                    <li><strong>Complex Implementation:</strong> Difficult to implement correctly</li>
                    <li><strong>Security Challenges:</strong> Vulnerable to Sybil and Eclipse attacks</li>
                    <li><strong>Churn Handling:</strong> Performance degrades with high node turnover</li>
                    <li><strong>Consistency Issues:</strong> Eventual consistency model</li>
                    <li><strong>Network Overhead:</strong> Maintenance messages consume bandwidth</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2><span class="emoji">🚀</span>Applications in Decentralized Systems</h2>

            <div class="example-box">
                <h3>Real-World Examples</h3>
                <ul>
                    <li><strong>BitTorrent:</strong> Uses DHT for trackerless peer discovery</li>
                    <li><strong>IPFS:</strong> Content addressing and routing using Kademlia DHT</li>
                    <li><strong>Ethereum:</strong> Node discovery and peer-to-peer networking</li>
                    <li><strong>Blockchain Storage:</strong> Off-chain storage solutions use DHT principles</li>
                    <li><strong>Decentralized DNS:</strong> Alternative domain name systems</li>
                </ul>
            </div>
        </div>

    </div>
</body>
</html>
