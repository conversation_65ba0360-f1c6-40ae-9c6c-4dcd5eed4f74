<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sphygmomanometer - Blood Pressure Measurement System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        .overview-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #e74c3c;
        }
        .diagram-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .component-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #17a2b8;
            transition: transform 0.3s ease;
        }
        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .component-card h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.3em;
        }
        .clinical-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f39c12;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .dual-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .dual-column {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🩺 Sphygmomanometer - Blood Pressure Measurement System</h1>

        <div class="overview-section">
            <h2>Overview of Sphygmomanometry</h2>
            <p>The sphygmomanometer is a fundamental medical instrument designed to measure arterial blood pressure non-invasively using the oscillometric or auscultatory method. This essential diagnostic tool consists of an inflatable cuff, pressure measurement system, and detection mechanism that work together to determine systolic and diastolic blood pressure values. Modern digital sphygmomanometers integrate advanced pressure sensors, automated inflation/deflation systems, and sophisticated algorithms to provide accurate, reliable blood pressure measurements for clinical diagnosis, patient monitoring, and cardiovascular health assessment in various healthcare settings.</p>
        </div>

        <div class="section-title">🔧 Sphygmomanometer System Block Diagram & Architecture</div>

        <div class="diagram-container">
            <svg width="100%" height="800" viewBox="0 0 1200 800">
                <!-- Title -->
                <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold">SPHYGMOMANOMETER SYSTEM BLOCK DIAGRAM</text>

                <!-- Patient arm and cuff -->
                <g>
                    <text x="150" y="80" text-anchor="middle" font-size="14" font-weight="bold">PATIENT INTERFACE</text>

                    <!-- Arm representation -->
                    <ellipse cx="150" cy="140" rx="30" ry="60" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="150" y="145" text-anchor="middle" font-size="12" font-weight="bold">ARM</text>

                    <!-- Blood pressure cuff -->
                    <rect x="120" y="120" width="60" height="40" fill="#2196f3" stroke="#1976d2" stroke-width="2" rx="5"/>
                    <text x="150" y="140" text-anchor="middle" font-size="10" fill="white">CUFF</text>

                    <!-- Artery -->
                    <line x1="140" y1="130" x2="160" y2="130" stroke="#f44336" stroke-width="4"/>
                    <text x="150" y="125" text-anchor="middle" font-size="8">Brachial Artery</text>

                    <!-- Stethoscope (for manual method) -->
                    <circle cx="120" cy="180" r="8" fill="#666"/>
                    <line x1="120" y1="180" x2="100" y2="200" stroke="#666" stroke-width="2"/>
                    <text x="90" y="210" font-size="8">Stethoscope</text>

                    <text x="150" y="220" text-anchor="middle" font-size="10">Measurement Site</text>
                </g>

                <!-- Pressure sensor -->
                <g>
                    <rect x="280" y="100" width="120" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="340" y="125" text-anchor="middle" font-size="12" font-weight="bold">PRESSURE SENSOR</text>
                    <text x="340" y="140" text-anchor="middle" font-size="10">• Piezoelectric/capacitive</text>
                    <text x="340" y="155" text-anchor="middle" font-size="10">• Range: 0-300 mmHg</text>
                    <text x="340" y="170" text-anchor="middle" font-size="10">• Accuracy: ±3 mmHg</text>
                </g>

                <!-- Signal conditioning -->
                <g>
                    <rect x="450" y="100" width="120" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="3"/>
                    <text x="510" y="125" text-anchor="middle" font-size="12" font-weight="bold">SIGNAL</text>
                    <text x="510" y="140" text-anchor="middle" font-size="12" font-weight="bold">CONDITIONING</text>
                    <text x="510" y="155" text-anchor="middle" font-size="10">• Amplification</text>
                    <text x="510" y="170" text-anchor="middle" font-size="10">• Filtering</text>
                </g>

                <!-- ADC -->
                <g>
                    <rect x="620" y="100" width="120" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="3"/>
                    <text x="680" y="125" text-anchor="middle" font-size="12" font-weight="bold">ADC CONVERTER</text>
                    <text x="680" y="140" text-anchor="middle" font-size="10">• 12-16 bit resolution</text>
                    <text x="680" y="155" text-anchor="middle" font-size="10">• 100-1000 Hz sampling</text>
                    <text x="680" y="170" text-anchor="middle" font-size="10">• Digital output</text>
                </g>

                <!-- Microprocessor -->
                <g>
                    <rect x="790" y="100" width="120" height="80" fill="#bbdefb" stroke="#2196f3" stroke-width="3"/>
                    <text x="850" y="125" text-anchor="middle" font-size="12" font-weight="bold">MICROPROCESSOR</text>
                    <text x="850" y="140" text-anchor="middle" font-size="10">• Algorithm processing</text>
                    <text x="850" y="155" text-anchor="middle" font-size="10">• Oscillometric analysis</text>
                    <text x="850" y="170" text-anchor="middle" font-size="10">• BP calculation</text>
                </g>

                <!-- Pump system -->
                <g>
                    <rect x="960" y="100" width="120" height="80" fill="#dcedc8" stroke="#689f38" stroke-width="3"/>
                    <text x="1020" y="125" text-anchor="middle" font-size="12" font-weight="bold">PUMP SYSTEM</text>
                    <text x="1020" y="140" text-anchor="middle" font-size="10">• Electric pump</text>
                    <text x="1020" y="155" text-anchor="middle" font-size="10">• Controlled deflation</text>
                    <text x="1020" y="170" text-anchor="middle" font-size="10">• Pressure regulation</text>
                </g>

                <!-- Signal flow arrows -->
                <path d="M 200 140 L 280 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 400 140 L 450 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 570 140 L 620 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 740 140 L 790 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>
                <path d="M 910 140 L 960 140" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrow)"/>

                <!-- Control feedback -->
                <path d="M 1020 180 L 1020 220 L 150 220 L 150 200" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrow)" stroke-dasharray="5,5"/>
                <text x="600" y="235" text-anchor="middle" font-size="10">Pressure Control Feedback</text>

                <!-- Measurement process -->
                <g>
                    <rect x="400" y="280" width="400" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                    <text x="600" y="310" text-anchor="middle" font-size="14" font-weight="bold">MEASUREMENT PROCESS</text>
                    <text x="600" y="330" text-anchor="middle" font-size="12">1. Cuff inflation above systolic pressure</text>
                    <text x="600" y="350" text-anchor="middle" font-size="12">2. Controlled deflation with pressure monitoring</text>
                    <text x="600" y="370" text-anchor="middle" font-size="12">3. Oscillometric pattern analysis</text>
                    <text x="600" y="390" text-anchor="middle" font-size="12">4. Systolic/diastolic pressure calculation</text>
                </g>

                <!-- Output systems -->
                <g>
                    <text x="600" y="450" text-anchor="middle" font-size="16" font-weight="bold">OUTPUT & DISPLAY SYSTEMS</text>

                    <!-- Digital display -->
                    <rect x="150" y="480" width="200" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                    <text x="250" y="505" text-anchor="middle" font-size="12" font-weight="bold">DIGITAL DISPLAY</text>
                    <rect x="170" y="520" width="160" height="60" fill="#000" stroke="#333" stroke-width="1"/>
                    <text x="250" y="540" text-anchor="middle" font-size="16" fill="#0f0" font-weight="bold">120/80</text>
                    <text x="250" y="555" text-anchor="middle" font-size="12" fill="#0f0">mmHg</text>
                    <text x="250" y="570" text-anchor="middle" font-size="10" fill="#0f0">HR: 72 bpm</text>
                    <text x="250" y="590" text-anchor="middle" font-size="10">Real-time readings</text>

                    <!-- Data storage -->
                    <rect x="400" y="480" width="200" height="120" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="500" y="505" text-anchor="middle" font-size="12" font-weight="bold">DATA STORAGE</text>
                    <text x="500" y="525" text-anchor="middle" font-size="10">• Memory for readings</text>
                    <text x="500" y="540" text-anchor="middle" font-size="10">• Date/time stamps</text>
                    <text x="500" y="555" text-anchor="middle" font-size="10">• User profiles</text>
                    <text x="500" y="570" text-anchor="middle" font-size="10">• Trend analysis</text>
                    <text x="500" y="585" text-anchor="middle" font-size="10">• Data export</text>

                    <!-- Communication -->
                    <rect x="650" y="480" width="200" height="120" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="750" y="505" text-anchor="middle" font-size="12" font-weight="bold">COMMUNICATION</text>
                    <text x="750" y="525" text-anchor="middle" font-size="10">• Bluetooth/WiFi</text>
                    <text x="750" y="540" text-anchor="middle" font-size="10">• Mobile app sync</text>
                    <text x="750" y="555" text-anchor="middle" font-size="10">• Cloud storage</text>
                    <text x="750" y="570" text-anchor="middle" font-size="10">• EMR integration</text>
                    <text x="750" y="585" text-anchor="middle" font-size="10">• Remote monitoring</text>

                    <!-- Alarm system -->
                    <rect x="900" y="480" width="200" height="120" fill="#ffcdd2" stroke="#f44336" stroke-width="2"/>
                    <text x="1000" y="505" text-anchor="middle" font-size="12" font-weight="bold">ALARM SYSTEM</text>
                    <circle cx="1000" cy="535" r="15" fill="#f44336"/>
                    <text x="1000" y="540" text-anchor="middle" font-size="10" fill="white">!</text>
                    <text x="1000" y="555" text-anchor="middle" font-size="10">• Hypertension alerts</text>
                    <text x="1000" y="570" text-anchor="middle" font-size="10">• Irregular heartbeat</text>
                    <text x="1000" y="585" text-anchor="middle" font-size="10">• System errors</text>
                </g>

                <!-- Connection lines -->
                <path d="M 850 180 L 850 220 L 600 220 L 600 280" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 400 L 250 400 L 250 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 400 L 500 400 L 500 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 400 L 750 400 L 750 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>
                <path d="M 600 400 L 1000 400 L 1000 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrow)"/>

                <!-- Power and calibration systems -->
                <g>
                    <text x="200" y="650" font-size="14" font-weight="bold">POWER SYSTEM</text>
                    <rect x="100" y="670" width="200" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="200" y="695" text-anchor="middle" font-size="11">• Battery/AC powered</text>
                    <text x="200" y="710" text-anchor="middle" font-size="11">• Low power design</text>
                    <text x="200" y="725" text-anchor="middle" font-size="11">• Auto power-off</text>
                    <text x="200" y="740" text-anchor="middle" font-size="11">• Power indicators</text>
                </g>

                <g>
                    <text x="600" y="650" font-size="14" font-weight="bold">CALIBRATION SYSTEM</text>
                    <rect x="500" y="670" width="200" height="80" fill="#e1bee7" stroke="#8e24aa" stroke-width="2"/>
                    <text x="600" y="695" text-anchor="middle" font-size="11">• Pressure calibration</text>
                    <text x="600" y="710" text-anchor="middle" font-size="11">• Reference standards</text>
                    <text x="600" y="725" text-anchor="middle" font-size="11">• Automatic verification</text>
                    <text x="600" y="740" text-anchor="middle" font-size="11">• Traceability</text>
                </g>

                <g>
                    <text x="1000" y="650" font-size="14" font-weight="bold">SAFETY FEATURES</text>
                    <rect x="900" y="670" width="200" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="2"/>
                    <text x="1000" y="695" text-anchor="middle" font-size="11">• Over-pressure protection</text>
                    <text x="1000" y="710" text-anchor="middle" font-size="11">• Auto-deflation</text>
                    <text x="1000" y="725" text-anchor="middle" font-size="11">• Error detection</text>
                    <text x="1000" y="740" text-anchor="middle" font-size="11">• User safety alerts</text>
                </g>

                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="section-title">🩺 Blood Pressure Measurement Methods & Procedures</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>🔴 Oscillometric Method (Automated)</h3>
                <p><strong>Principle:</strong> Detects arterial wall oscillations</p>
                <p><strong>Measurement:</strong> Pressure oscillations during deflation</p>
                <p><strong>Systolic:</strong> Point of maximum oscillation amplitude</p>
                <p><strong>Diastolic:</strong> Calculated from oscillation pattern</p>
                <p><strong>Advantages:</strong> Automated, no operator skill required</p>
                <p><strong>Accuracy:</strong> ±3 mmHg (when properly calibrated)</p>
                <p><strong>Applications:</strong> Home monitoring, clinical use</p>
            </div>

            <div class="component-card">
                <h3>🔵 Auscultatory Method (Manual)</h3>
                <p><strong>Principle:</strong> Korotkoff sounds detection</p>
                <p><strong>Equipment:</strong> Stethoscope + manual cuff</p>
                <p><strong>Systolic:</strong> First Korotkoff sound (Phase I)</p>
                <p><strong>Diastolic:</strong> Disappearance of sounds (Phase V)</p>
                <p><strong>Advantages:</strong> Gold standard, highly accurate</p>
                <p><strong>Disadvantages:</strong> Operator dependent, skill required</p>
                <p><strong>Applications:</strong> Clinical diagnosis, validation</p>
            </div>

            <div class="component-card">
                <h3>🟡 Cuff Selection & Sizing</h3>
                <p><strong>Cuff Width:</strong> 40% of arm circumference</p>
                <p><strong>Bladder Length:</strong> 80% of arm circumference</p>
                <p><strong>Adult Standard:</strong> 12-13 cm width</p>
                <p><strong>Large Adult:</strong> 15-16 cm width</p>
                <p><strong>Pediatric:</strong> 8-9 cm width</p>
                <p><strong>Incorrect Size Effects:</strong> False high/low readings</p>
                <p><strong>Placement:</strong> 2-3 cm above antecubital fossa</p>
            </div>

            <div class="component-card">
                <h3>🟠 Measurement Procedure</h3>
                <p><strong>1. Patient Preparation:</strong> 5-min rest, seated position</p>
                <p><strong>2. Cuff Application:</strong> Proper size, snug fit</p>
                <p><strong>3. Arm Position:</strong> Heart level, supported</p>
                <p><strong>4. Inflation:</strong> 20-30 mmHg above systolic</p>
                <p><strong>5. Deflation:</strong> 2-3 mmHg/second rate</p>
                <p><strong>6. Multiple Readings:</strong> 2-3 measurements, 1-min apart</p>
            </div>

            <div class="component-card">
                <h3>⚡ Digital Processing Algorithms</h3>
                <p><strong>Signal Filtering:</strong> Remove noise, artifacts</p>
                <p><strong>Oscillation Detection:</strong> Peak identification</p>
                <p><strong>Envelope Analysis:</strong> Amplitude pattern recognition</p>
                <p><strong>Pressure Calculation:</strong> Systolic/diastolic determination</p>
                <p><strong>Quality Assessment:</strong> Measurement validity check</p>
                <p><strong>Artifact Rejection:</strong> Motion, irregular rhythm</p>
            </div>

            <div class="component-card">
                <h3>📊 Blood Pressure Classifications</h3>
                <p><strong>Normal:</strong> <120/80 mmHg</p>
                <p><strong>Elevated:</strong> 120-129/<80 mmHg</p>
                <p><strong>Stage 1 HTN:</strong> 130-139/80-89 mmHg</p>
                <p><strong>Stage 2 HTN:</strong> ≥140/90 mmHg</p>
                <p><strong>Hypertensive Crisis:</strong> >180/120 mmHg</p>
                <p><strong>Hypotension:</strong> <90/60 mmHg</p>
            </div>
        </div>

        <div class="section-title">🏥 Clinical Applications & Diagnostic Uses</div>

        <div class="clinical-section">
            <h2>Sphygmomanometry in Clinical Practice</h2>
            <p>Blood pressure measurement is fundamental to cardiovascular health assessment, hypertension diagnosis, and treatment monitoring. Sphygmomanometers are essential in routine clinical examinations, emergency medicine, surgical monitoring, and home healthcare management. Accurate blood pressure measurement guides therapeutic decisions, medication adjustments, and cardiovascular risk stratification across all medical specialties.</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Clinical Setting</th>
                    <th>Measurement Type</th>
                    <th>Frequency</th>
                    <th>Key Considerations</th>
                    <th>Target Accuracy</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Primary Care</strong></td>
                    <td>Automated/Manual</td>
                    <td>Every visit</td>
                    <td>Proper technique, multiple readings</td>
                    <td>±3 mmHg</td>
                </tr>
                <tr>
                    <td><strong>Home Monitoring</strong></td>
                    <td>Automated</td>
                    <td>Daily/Weekly</td>
                    <td>Patient education, validated devices</td>
                    <td>±3 mmHg</td>
                </tr>
                <tr>
                    <td><strong>Hospital/ICU</strong></td>
                    <td>Automated continuous</td>
                    <td>Continuous/Hourly</td>
                    <td>Alarm settings, trending</td>
                    <td>±2 mmHg</td>
                </tr>
                <tr>
                    <td><strong>Ambulatory Monitoring</strong></td>
                    <td>24-hour ABPM</td>
                    <td>Every 15-30 min</td>
                    <td>White coat effect, nocturnal dipping</td>
                    <td>±3 mmHg</td>
                </tr>
                <tr>
                    <td><strong>Emergency Medicine</strong></td>
                    <td>Automated rapid</td>
                    <td>As needed</td>
                    <td>Rapid assessment, shock detection</td>
                    <td>±5 mmHg</td>
                </tr>
            </tbody>
        </table>

        <div class="section-title">⚙️ Technical Specifications & Maintenance</div>

        <div class="dual-column">
            <div class="clinical-section">
                <h3>System Specifications</h3>
                <ul>
                    <li><span class="highlight">Pressure Range:</span> 0-300 mmHg</li>
                    <li><span class="highlight">Accuracy:</span> ±3 mmHg or ±2%</li>
                    <li><span class="highlight">Resolution:</span> 1 mmHg</li>
                    <li><span class="highlight">Cuff Pressure:</span> 0-300 mmHg</li>
                    <li><span class="highlight">Deflation Rate:</span> 2-3 mmHg/second</li>
                    <li><span class="highlight">Measurement Time:</span> 30-60 seconds</li>
                    <li><span class="highlight">Memory:</span> 50-200 readings</li>
                    <li><span class="highlight">Power:</span> Battery/AC adapter</li>
                </ul>
            </div>

            <div class="clinical-section">
                <h3>Calibration & Maintenance</h3>
                <ul>
                    <li><span class="highlight">Daily:</span> Visual inspection, function check</li>
                    <li><span class="highlight">Weekly:</span> Cuff inspection, tube check</li>
                    <li><span class="highlight">Monthly:</span> Accuracy verification</li>
                    <li><span class="highlight">Annually:</span> Professional calibration</li>
                    <li><span class="highlight">Calibration Standard:</span> Mercury manometer</li>
                    <li><span class="highlight">Cuff Care:</span> Clean, inspect for leaks</li>
                    <li><span class="highlight">Storage:</span> Proper coiling, dry environment</li>
                    <li><span class="highlight">Documentation:</span> Calibration certificates</li>
                </ul>
            </div>
        </div>

        <div class="section-title">🔧 Troubleshooting & Safety Protocols</div>

        <div class="component-grid">
            <div class="component-card">
                <h3>⚠️ Common Issues & Solutions</h3>
                <p><strong>Inaccurate Readings:</strong> Check cuff size, calibration</p>
                <p><strong>Cuff Won't Inflate:</strong> Check connections, pump function</p>
                <p><strong>Slow Deflation:</strong> Inspect valve, tubing for leaks</p>
                <p><strong>Error Messages:</strong> Check cuff placement, patient movement</p>
                <p><strong>Inconsistent Results:</strong> Multiple measurements, proper technique</p>
                <p><strong>Display Issues:</strong> Check power, reset device</p>
            </div>

            <div class="component-card">
                <h3>🛡️ Safety & Compliance</h3>
                <p><strong>Medical Device Standards:</strong> IEC 80601-2-30</p>
                <p><strong>Accuracy Standards:</strong> AAMI/ESH/BHS protocols</p>
                <p><strong>Over-pressure Protection:</strong> 300 mmHg maximum</p>
                <p><strong>Auto-deflation:</strong> Safety timeout feature</p>
                <p><strong>Infection Control:</strong> Disposable cuffs available</p>
                <p><strong>Patient Safety:</strong> Proper cuff sizing essential</p>
            </div>

            <div class="component-card">
                <h3>📊 Quality Assurance</h3>
                <p><strong>Validation Protocols:</strong> AAMI/ESH/BHS standards</p>
                <p><strong>Calibration Tracking:</strong> Regular verification schedule</p>
                <p><strong>Performance Monitoring:</strong> Accuracy trending</p>
                <p><strong>User Training:</strong> Proper technique education</p>
                <p><strong>Data Integrity:</strong> Measurement logging</p>
                <p><strong>Audit Compliance:</strong> Regulatory requirements</p>
            </div>
        </div>

    </div>
</body>
</html>
